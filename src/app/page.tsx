'use client'

import { <PERSON><PERSON>, Card, CardBody, CardHeader, Input, Navbar, NavbarBrand, NavbarContent, NavbarItem } from "@heroui/react";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar>
        <NavbarBrand>
          <p className="font-bold text-inherit">HeroUI + Next.js</p>
        </NavbarBrand>
        <NavbarContent justify="end">
          <NavbarItem>
            <Button color="primary" variant="flat">
              登录
            </Button>
          </NavbarItem>
        </NavbarContent>
      </Navbar>

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">欢迎使用 HeroUI</h1>
          <p className="text-lg text-default-600">
            一个美观、快速、现代的 React UI 库
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">组件丰富</h3>
            </CardHeader>
            <CardBody>
              <p>提供 50+ 个高质量的 React 组件，满足各种开发需求。</p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">主题定制</h3>
            </CardHeader>
            <CardBody>
              <p>基于 Tailwind CSS，支持深度定制主题和样式。</p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">无障碍访问</h3>
            </CardHeader>
            <CardBody>
              <p>基于 React Aria 构建，确保良好的可访问性体验。</p>
            </CardBody>
          </Card>
        </div>

        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">试试组件</h3>
            </CardHeader>
            <CardBody className="space-y-4">
              <Input
                label="邮箱"
                placeholder="输入你的邮箱"
                type="email"
              />
              <Input
                label="密码"
                placeholder="输入密码"
                type="password"
              />
              <Button color="primary" className="w-full">
                提交
              </Button>
            </CardBody>
          </Card>
        </div>
      </main>
    </div>
  );
}
