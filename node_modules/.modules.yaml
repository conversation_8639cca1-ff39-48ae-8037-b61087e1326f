hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ast-grep/napi-darwin-arm64@0.35.0':
    '@ast-grep/napi-darwin-arm64': private
  '@ast-grep/napi-darwin-x64@0.35.0':
    '@ast-grep/napi-darwin-x64': private
  '@ast-grep/napi-linux-arm64-gnu@0.35.0':
    '@ast-grep/napi-linux-arm64-gnu': private
  '@ast-grep/napi-linux-arm64-musl@0.35.0':
    '@ast-grep/napi-linux-arm64-musl': private
  '@ast-grep/napi-linux-x64-gnu@0.35.0':
    '@ast-grep/napi-linux-x64-gnu': private
  '@ast-grep/napi-linux-x64-musl@0.35.0':
    '@ast-grep/napi-linux-x64-musl': private
  '@ast-grep/napi-win32-arm64-msvc@0.35.0':
    '@ast-grep/napi-win32-arm64-msvc': private
  '@ast-grep/napi-win32-ia32-msvc@0.35.0':
    '@ast-grep/napi-win32-ia32-msvc': private
  '@ast-grep/napi-win32-x64-msvc@0.35.0':
    '@ast-grep/napi-win32-x64-msvc': private
  '@ast-grep/napi@0.35.0':
    '@ast-grep/napi': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/crc32c@5.2.0':
    '@aws-crypto/crc32c': private
  '@aws-crypto/ie11-detection@3.0.0':
    '@aws-crypto/ie11-detection': private
  '@aws-crypto/sha1-browser@5.2.0':
    '@aws-crypto/sha1-browser': private
  '@aws-crypto/sha256-browser@3.0.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@3.0.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-cloudfront@3.398.0':
    '@aws-sdk/client-cloudfront': private
  '@aws-sdk/client-dynamodb@3.848.0':
    '@aws-sdk/client-dynamodb': private
  '@aws-sdk/client-lambda@3.851.0':
    '@aws-sdk/client-lambda': private
  '@aws-sdk/client-s3@3.850.0':
    '@aws-sdk/client-s3': private
  '@aws-sdk/client-sqs@3.854.0':
    '@aws-sdk/client-sqs': private
  '@aws-sdk/client-sso@3.398.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/client-sts@3.398.0':
    '@aws-sdk/client-sts': private
  '@aws-sdk/core@3.846.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-env@3.398.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.846.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.398.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.398.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.398.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.398.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.398.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/endpoint-cache@3.804.0':
    '@aws-sdk/endpoint-cache': private
  '@aws-sdk/middleware-bucket-endpoint@3.840.0':
    '@aws-sdk/middleware-bucket-endpoint': private
  '@aws-sdk/middleware-endpoint-discovery@3.840.0':
    '@aws-sdk/middleware-endpoint-discovery': private
  '@aws-sdk/middleware-expect-continue@3.840.0':
    '@aws-sdk/middleware-expect-continue': private
  '@aws-sdk/middleware-flexible-checksums@3.846.0':
    '@aws-sdk/middleware-flexible-checksums': private
  '@aws-sdk/middleware-host-header@3.398.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-location-constraint@3.840.0':
    '@aws-sdk/middleware-location-constraint': private
  '@aws-sdk/middleware-logger@3.398.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.398.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-sdk-s3@3.846.0':
    '@aws-sdk/middleware-sdk-s3': private
  '@aws-sdk/middleware-sdk-sqs@3.845.0':
    '@aws-sdk/middleware-sdk-sqs': private
  '@aws-sdk/middleware-sdk-sts@3.398.0':
    '@aws-sdk/middleware-sdk-sts': private
  '@aws-sdk/middleware-signing@3.398.0':
    '@aws-sdk/middleware-signing': private
  '@aws-sdk/middleware-ssec@3.840.0':
    '@aws-sdk/middleware-ssec': private
  '@aws-sdk/middleware-user-agent@3.398.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.848.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.840.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/signature-v4-multi-region@3.846.0':
    '@aws-sdk/signature-v4-multi-region': private
  '@aws-sdk/token-providers@3.398.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.398.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-arn-parser@3.804.0':
    '@aws-sdk/util-arn-parser': private
  '@aws-sdk/util-endpoints@3.398.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.398.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.398.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@aws-sdk/xml-builder@3.310.0':
    '@aws-sdk/xml-builder': private
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@cloudflare/unenv-preset@2.4.1(unenv@2.0.0-rc.17)(workerd@1.20250712.0)':
    '@cloudflare/unenv-preset': private
  '@cloudflare/workerd-darwin-64@1.20250712.0':
    '@cloudflare/workerd-darwin-64': private
  '@cloudflare/workerd-darwin-arm64@1.20250712.0':
    '@cloudflare/workerd-darwin-arm64': private
  '@cloudflare/workerd-linux-64@1.20250712.0':
    '@cloudflare/workerd-linux-64': private
  '@cloudflare/workerd-linux-arm64@1.20250712.0':
    '@cloudflare/workerd-linux-arm64': private
  '@cloudflare/workerd-windows-64@1.20250712.0':
    '@cloudflare/workerd-windows-64': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@dotenvx/dotenvx@1.31.0':
    '@dotenvx/dotenvx': private
  '@ecies/ciphers@0.2.4(@noble/ciphers@1.3.0)':
    '@ecies/ciphers': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.32.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': public
  '@eslint/core@0.15.1':
    '@eslint/core': public
  '@eslint/js@9.32.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.4':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.3':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.3':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.2.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.2.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.2.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.2.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.2.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.2.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.2.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.2.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.2.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.3':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.3':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-ppc64@0.34.3':
    '@img/sharp-linux-ppc64': private
  '@img/sharp-linux-s390x@0.34.3':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.3':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.3':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.3':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.3':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.3':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.3':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.3.5':
    '@next/env': private
  '@next/eslint-plugin-next@15.3.5':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.3.5':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.5':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.5':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.5':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.5':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.5':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.5':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.5':
    '@next/swc-win32-x64-msvc': private
  '@noble/ciphers@1.3.0':
    '@noble/ciphers': private
  '@noble/curves@1.9.4':
    '@noble/curves': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@node-minify/core@8.0.6':
    '@node-minify/core': private
  '@node-minify/terser@8.0.6':
    '@node-minify/terser': private
  '@node-minify/utils@8.0.6':
    '@node-minify/utils': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opennextjs/aws@3.7.0':
    '@opennextjs/aws': private
  '@poppinss/colors@4.1.5':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.4':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.2':
    '@poppinss/exception': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': public
  '@sindresorhus/is@7.0.2':
    '@sindresorhus/is': private
  '@smithy/abort-controller@2.2.0':
    '@smithy/abort-controller': private
  '@smithy/chunked-blob-reader-native@4.0.0':
    '@smithy/chunked-blob-reader-native': private
  '@smithy/chunked-blob-reader@5.0.0':
    '@smithy/chunked-blob-reader': private
  '@smithy/config-resolver@2.2.0':
    '@smithy/config-resolver': private
  '@smithy/core@3.7.2':
    '@smithy/core': private
  '@smithy/credential-provider-imds@2.3.0':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.4':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.4':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.2':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.4':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.4':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@2.5.0':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-blob-browser@4.0.4':
    '@smithy/hash-blob-browser': private
  '@smithy/hash-node@2.2.0':
    '@smithy/hash-node': private
  '@smithy/hash-stream-node@4.0.4':
    '@smithy/hash-stream-node': private
  '@smithy/invalid-dependency@2.2.0':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@4.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/md5-js@4.0.4':
    '@smithy/md5-js': private
  '@smithy/middleware-content-length@2.2.0':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@2.5.1':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@2.3.1':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@2.3.0':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@2.2.0':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@2.3.0':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@2.5.0':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.4':
    '@smithy/property-provider': private
  '@smithy/protocol-http@2.0.5':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@2.2.0':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@2.2.0':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@2.1.5':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@2.4.0':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.1.2':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@2.5.1':
    '@smithy/smithy-client': private
  '@smithy/types@2.12.0':
    '@smithy/types': private
  '@smithy/url-parser@2.2.0':
    '@smithy/url-parser': private
  '@smithy/util-base64@2.3.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@2.2.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@2.3.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@2.2.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@2.2.1':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@2.3.1':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.6':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.4':
    '@smithy/util-middleware': private
  '@smithy/util-retry@2.2.0':
    '@smithy/util-retry': private
  '@smithy/util-stream@2.2.0':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@2.2.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@2.3.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@2.2.0':
    '@smithy/util-waiter': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@tsconfig/node18@1.0.3':
    '@tsconfig/node18': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/uuid@9.0.8':
    '@types/uuid': private
  '@typescript-eslint/eslint-plugin@8.38.0(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3))(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.38.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.38.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.38.0':
    '@typescript-eslint/visitor-keys': public
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@2.0.0:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.2:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws4fetch@1.0.20:
    aws4fetch: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  blake3-wasm@2.1.5:
    blake3-wasm: private
  body-parser@2.2.0:
    body-parser: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  client-only@0.0.1:
    client-only: private
  cliui@9.0.1:
    cliui: private
  cloudflare@4.5.0:
    cloudflare: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@11.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@1.0.2:
    cookie: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  detect-libc@2.0.4:
    detect-libc: private
  doctrine@2.1.0:
    doctrine: private
  dotenv@16.6.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  eciesjs@0.4.15:
    eciesjs: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  enquirer@2.4.1:
    enquirer: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.4:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@9.32.0(jiti@2.5.1)):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@9.32.0(jiti@2.5.1)):
    eslint-module-utils: public
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1)(eslint@9.32.0(jiti@2.5.1)):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.32.0(jiti@2.5.1)):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@9.32.0(jiti@2.5.1)):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@9.32.0(jiti@2.5.1)):
    eslint-plugin-react: public
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  execa@5.1.1:
    execa: private
  exit-hook@2.2.1:
    exit-hook: private
  express@5.0.1:
    express: private
  exsolve@1.0.7:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-xml-parser@4.2.5:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.4:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@11.0.3:
    glob: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@6.0.0:
    gzip-size: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@4.1.1:
    jackspeak: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@11.1.0:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mime@3.0.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  miniflare@4.20250712.2:
    miniflare: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  mnemonist@0.38.3:
    mnemonist: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.2:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@1.0.0:
    negotiator: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  npm-run-path@4.0.1:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-treeify@1.1.33:
    object-treeify: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  obliterator@1.6.1:
    obliterator: private
  ohash@2.0.11:
    ohash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-is@16.13.1:
    react-is: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  router@2.2.0:
    router: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sharp@0.34.3:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  stable-hash@0.0.5:
    stable-hash: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stoppable@1.1.0:
    stoppable: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@1.1.2:
    strnum: private
  styled-jsx@5.1.6(react@19.1.1):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  terser@5.16.9:
    terser: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-tqdm@0.8.6:
    ts-tqdm: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  undici@7.12.0:
    undici: private
  unenv@2.0.0-rc.17:
    unenv: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@10.1.0:
    urlpattern-polyfill: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@9.0.1:
    uuid: private
  vary@1.1.2:
    vary: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@4.0.0:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  workerd@1.20250712.0:
    workerd: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.0:
    ws: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@22.0.0:
    yargs-parser: private
  yargs@18.0.0:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  youch-core@0.3.3:
    youch-core: private
  youch@4.1.0-beta.10:
    youch: private
  zod@3.22.3:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.0
pendingBuilds: []
prunedAt: Mon, 28 Jul 2025 17:20:32 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@ast-grep/napi-darwin-arm64@0.35.0'
  - '@ast-grep/napi-linux-arm64-gnu@0.35.0'
  - '@ast-grep/napi-linux-arm64-musl@0.35.0'
  - '@ast-grep/napi-linux-x64-gnu@0.35.0'
  - '@ast-grep/napi-linux-x64-musl@0.35.0'
  - '@ast-grep/napi-win32-arm64-msvc@0.35.0'
  - '@ast-grep/napi-win32-ia32-msvc@0.35.0'
  - '@ast-grep/napi-win32-x64-msvc@0.35.0'
  - '@cloudflare/workerd-darwin-arm64@1.20250712.0'
  - '@cloudflare/workerd-linux-64@1.20250712.0'
  - '@cloudflare/workerd-linux-arm64@1.20250712.0'
  - '@cloudflare/workerd-windows-64@1.20250712.0'
  - '@emnapi/core@1.4.5'
  - '@emnapi/runtime@1.4.5'
  - '@emnapi/wasi-threads@1.0.4'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-x64@0.25.4'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@img/sharp-win32-x64@0.33.5'
  - '@img/sharp-win32-x64@0.34.3'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-arm64@15.3.5'
  - '@next/swc-linux-arm64-gnu@15.3.5'
  - '@next/swc-linux-arm64-musl@15.3.5'
  - '@next/swc-linux-x64-gnu@15.3.5'
  - '@next/swc-linux-x64-musl@15.3.5'
  - '@next/swc-win32-arm64-msvc@15.3.5'
  - '@next/swc-win32-x64-msvc@15.3.5'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-x64-msvc@1.11.1'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
