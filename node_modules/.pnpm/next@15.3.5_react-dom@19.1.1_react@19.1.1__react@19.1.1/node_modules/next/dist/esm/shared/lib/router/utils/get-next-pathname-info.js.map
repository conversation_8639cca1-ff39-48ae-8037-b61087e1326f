{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["normalizeLocalePath", "removePathPrefix", "pathHasPrefix", "getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "locales", "locale", "detectedLocale"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,aAAa,QAAQ,oBAAmB;AAkDjD,OAAO,SAASC,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,YAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYJ,cAAcQ,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGH,iBAAiBS,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIM,uBAAuBF,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACS,UAAU,CAAC,mBACzBH,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMG,QAAQJ,KAAKN,QAAQ,CACxBW,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBJ,KAAKO,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAU,AAAC,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAId,QAAQe,SAAS,KAAK,MAAM;YAC9BV,KAAKN,QAAQ,GAAGQ;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIL,MAAM;QACR,IAAIc,SAAShB,QAAQiB,YAAY,GAC7BjB,QAAQiB,YAAY,CAACC,OAAO,CAACb,KAAKN,QAAQ,IAC1CJ,oBAAoBU,KAAKN,QAAQ,EAAEG,KAAKiB,OAAO;QAEnDd,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACnBL;QAAhBX,KAAKN,QAAQ,GAAGiB,CAAAA,mBAAAA,OAAOjB,QAAQ,YAAfiB,mBAAmBX,KAAKN,QAAQ;QAEhD,IAAI,CAACiB,OAAOK,cAAc,IAAIhB,KAAKO,OAAO,EAAE;YAC1CI,SAAShB,QAAQiB,YAAY,GACzBjB,QAAQiB,YAAY,CAACC,OAAO,CAACX,wBAC7BZ,oBAAoBY,sBAAsBL,KAAKiB,OAAO;YAE1D,IAAIH,OAAOK,cAAc,EAAE;gBACzBhB,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACrC;QACF;IACF;IACA,OAAOhB;AACT"}