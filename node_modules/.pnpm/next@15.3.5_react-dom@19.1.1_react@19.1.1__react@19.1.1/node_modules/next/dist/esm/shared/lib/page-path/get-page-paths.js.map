{"version": 3, "sources": ["../../../../src/shared/lib/page-path/get-page-paths.ts"], "sourcesContent": ["import { denormalizePagePath } from './denormalize-page-path'\nimport path from '../isomorphic/path'\n\n/**\n * Calculate all possible pagePaths for a given normalized pagePath along with\n * allowed extensions. This can be used to check which one of the files exists\n * and to debug inspected locations.\n *\n * For pages, map `/route` to [`/route.[ext]`, `/route/index.[ext]`]\n * For app paths, map `/route/page` to [`/route/page.[ext]`] or `/route/route`\n * to [`/route/route.[ext]`]\n *\n * @param normalizedPagePath Normalized page path (it will denormalize).\n * @param extensions Allowed extensions.\n */\nexport function getPagePaths(\n  normalizedPagePath: string,\n  extensions: string[],\n  isAppDir: boolean\n) {\n  const page = denormalizePagePath(normalizedPagePath)\n\n  let prefixes: string[]\n  if (isAppDir) {\n    prefixes = [page]\n  } else if (normalizedPagePath.endsWith('/index')) {\n    prefixes = [path.join(page, 'index')]\n  } else {\n    prefixes = [page, path.join(page, 'index')]\n  }\n\n  const paths: string[] = []\n  for (const extension of extensions) {\n    for (const prefix of prefixes) {\n      paths.push(`${prefix}.${extension}`)\n    }\n  }\n\n  return paths\n}\n"], "names": ["denormalizePagePath", "path", "getPagePaths", "normalizedPagePath", "extensions", "isAppDir", "page", "prefixes", "endsWith", "join", "paths", "extension", "prefix", "push"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAAyB;AAC7D,OAAOC,UAAU,qBAAoB;AAErC;;;;;;;;;;;CAWC,GACD,OAAO,SAASC,aACdC,kBAA0B,EAC1BC,UAAoB,EACpBC,QAAiB;IAEjB,MAAMC,OAAON,oBAAoBG;IAEjC,IAAII;IACJ,IAAIF,UAAU;QACZE,WAAW;YAACD;SAAK;IACnB,OAAO,IAAIH,mBAAmBK,QAAQ,CAAC,WAAW;QAChDD,WAAW;YAACN,KAAKQ,IAAI,CAACH,MAAM;SAAS;IACvC,OAAO;QACLC,WAAW;YAACD;YAAML,KAAKQ,IAAI,CAACH,MAAM;SAAS;IAC7C;IAEA,MAAMI,QAAkB,EAAE;IAC1B,KAAK,MAAMC,aAAaP,WAAY;QAClC,KAAK,MAAMQ,UAAUL,SAAU;YAC7BG,MAAMG,IAAI,CAAC,AAAGD,SAAO,MAAGD;QAC1B;IACF;IAEA,OAAOD;AACT"}