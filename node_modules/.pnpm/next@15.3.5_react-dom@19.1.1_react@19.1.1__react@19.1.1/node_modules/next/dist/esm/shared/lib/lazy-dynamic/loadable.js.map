{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/loadable.tsx"], "sourcesContent": ["import { Suspense, Fragment, lazy } from 'react'\nimport { BailoutToCSR } from './dynamic-bailout-to-csr'\nimport type { ComponentModule } from './types'\nimport { PreloadChunks } from './preload-chunks'\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(\n  mod: React.ComponentType<P> | ComponentModule<P> | undefined\n): {\n  default: React.ComponentType<P>\n} {\n  // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n  // Cases:\n  // mod: { default: Component }\n  // mod: Component\n  // mod: { default: proxy(Component) }\n  // mod: proxy(Component)\n  const hasDefault = mod && 'default' in mod\n  return {\n    default: hasDefault\n      ? (mod as ComponentModule<P>).default\n      : (mod as React.ComponentType<P>),\n  }\n}\n\nconst defaultOptions = {\n  loader: () => Promise.resolve(convertModule(() => null)),\n  loading: null,\n  ssr: true,\n}\n\ninterface LoadableOptions {\n  loader?: () => Promise<React.ComponentType<any> | ComponentModule<any>>\n  loading?: React.ComponentType<any> | null\n  ssr?: boolean\n  modules?: string[]\n}\n\nfunction Loadable(options: LoadableOptions) {\n  const opts = { ...defaultOptions, ...options }\n  const Lazy = lazy(() => opts.loader().then(convertModule))\n  const Loading = opts.loading\n\n  function LoadableComponent(props: any) {\n    const fallbackElement = Loading ? (\n      <Loading isLoading={true} pastDelay={true} error={null} />\n    ) : null\n\n    // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n    const hasSuspenseBoundary = !opts.ssr || !!opts.loading\n    const Wrap = hasSuspenseBoundary ? Suspense : Fragment\n    const wrapProps = hasSuspenseBoundary ? { fallback: fallbackElement } : {}\n    const children = opts.ssr ? (\n      <>\n        {/* During SSR, we need to preload the CSS from the dynamic component to avoid flash of unstyled content */}\n        {typeof window === 'undefined' ? (\n          <PreloadChunks moduleIds={opts.modules} />\n        ) : null}\n        <Lazy {...props} />\n      </>\n    ) : (\n      <BailoutToCSR reason=\"next/dynamic\">\n        <Lazy {...props} />\n      </BailoutToCSR>\n    )\n\n    return <Wrap {...wrapProps}>{children}</Wrap>\n  }\n\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return LoadableComponent\n}\n\nexport default Loadable\n"], "names": ["Suspense", "Fragment", "lazy", "BailoutToCSR", "PreloadChunks", "convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "hasSuspenseBoundary", "Wrap", "wrapProps", "fallback", "children", "window", "moduleIds", "modules", "reason", "displayName"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,QAAO;AAChD,SAASC,YAAY,QAAQ,2BAA0B;AAEvD,SAASC,aAAa,QAAQ,mBAAkB;AAEhD,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,qCAAqC;IACrC,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACL,AAACD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,qBAAOhB,KAAK,IAAMe,KAAKP,MAAM,GAAGS,IAAI,CAACd;IAC3C,MAAMe,UAAUH,KAAKJ,OAAO;IAE5B,SAASQ,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,wBACtB,KAACA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,kFAAkF;QAClF,MAAMC,sBAAsB,CAACV,KAAKH,GAAG,IAAI,CAAC,CAACG,KAAKJ,OAAO;QACvD,MAAMe,OAAOD,sBAAsB3B,WAAWC;QAC9C,MAAM4B,YAAYF,sBAAsB;YAAEG,UAAUP;QAAgB,IAAI,CAAC;QACzE,MAAMQ,WAAWd,KAAKH,GAAG,iBACvB;;gBAEG,OAAOkB,WAAW,4BACjB,KAAC5B;oBAAc6B,WAAWhB,KAAKiB,OAAO;qBACpC;8BACJ,KAAChB;oBAAM,GAAGI,KAAK;;;2BAGjB,KAACnB;YAAagC,QAAO;sBACnB,cAAA,KAACjB;gBAAM,GAAGI,KAAK;;;QAInB,qBAAO,KAACM;YAAM,GAAGC,SAAS;sBAAGE;;IAC/B;IAEAV,kBAAkBe,WAAW,GAAG;IAEhC,OAAOf;AACT;AAEA,eAAeN,SAAQ"}