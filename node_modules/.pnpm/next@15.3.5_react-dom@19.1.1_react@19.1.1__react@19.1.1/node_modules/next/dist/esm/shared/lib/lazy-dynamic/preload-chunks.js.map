{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/preload-chunks.tsx"], "sourcesContent": ["'use client'\n\nimport { preload } from 'react-dom'\n\nimport { workAsyncStorage } from '../../../server/app-render/work-async-storage.external'\nimport { encodeURIPath } from '../encode-uri-path'\n\nexport function PreloadChunks({\n  moduleIds,\n}: {\n  moduleIds: string[] | undefined\n}) {\n  // Early return in client compilation and only load requestStore on server side\n  if (typeof window !== 'undefined') {\n    return null\n  }\n\n  const workStore = workAsyncStorage.getStore()\n  if (workStore === undefined) {\n    return null\n  }\n\n  const allFiles = []\n\n  // Search the current dynamic call unique key id in react loadable manifest,\n  // and find the corresponding CSS files to preload\n  if (workStore.reactLoadableManifest && moduleIds) {\n    const manifest = workStore.reactLoadableManifest\n    for (const key of moduleIds) {\n      if (!manifest[key]) continue\n      const chunks = manifest[key].files\n      allFiles.push(...chunks)\n    }\n  }\n\n  if (allFiles.length === 0) {\n    return null\n  }\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n\n  return (\n    <>\n      {allFiles.map((chunk) => {\n        const href = `${workStore.assetPrefix}/_next/${encodeURIPath(chunk)}${dplId}`\n        const isCss = chunk.endsWith('.css')\n        // If it's stylesheet we use `precedence` o help hoist with React Float.\n        // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n        // The `preload` for stylesheet is not optional.\n        if (isCss) {\n          return (\n            <link\n              key={chunk}\n              // @ts-ignore\n              precedence=\"dynamic\"\n              href={href}\n              rel=\"stylesheet\"\n              as=\"style\"\n            />\n          )\n        } else {\n          // If it's script we use ReactDOM.preload to preload the resources\n          preload(href, {\n            as: 'script',\n            fetchPriority: 'low',\n          })\n          return null\n        }\n      })}\n    </>\n  )\n}\n"], "names": ["preload", "workAsyncStorage", "encodeURIPath", "PreloadChunks", "moduleIds", "window", "workStore", "getStore", "undefined", "allFiles", "reactLoadableManifest", "manifest", "key", "chunks", "files", "push", "length", "dplId", "process", "env", "NEXT_DEPLOYMENT_ID", "map", "chunk", "href", "assetPrefix", "isCss", "endsWith", "link", "precedence", "rel", "as", "fetchPriority"], "mappings": "AAAA;;AAEA,SAASA,OAAO,QAAQ,YAAW;AAEnC,SAASC,gBAAgB,QAAQ,yDAAwD;AACzF,SAASC,aAAa,QAAQ,qBAAoB;AAElD,OAAO,SAASC,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,+EAA+E;IAC/E,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IAEA,MAAMC,YAAYL,iBAAiBM,QAAQ;IAC3C,IAAID,cAAcE,WAAW;QAC3B,OAAO;IACT;IAEA,MAAMC,WAAW,EAAE;IAEnB,4EAA4E;IAC5E,kDAAkD;IAClD,IAAIH,UAAUI,qBAAqB,IAAIN,WAAW;QAChD,MAAMO,WAAWL,UAAUI,qBAAqB;QAChD,KAAK,MAAME,OAAOR,UAAW;YAC3B,IAAI,CAACO,QAAQ,CAACC,IAAI,EAAE;YACpB,MAAMC,SAASF,QAAQ,CAACC,IAAI,CAACE,KAAK;YAClCL,SAASM,IAAI,IAAIF;QACnB;IACF;IAEA,IAAIJ,SAASO,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAMC,QAAQC,QAAQC,GAAG,CAACC,kBAAkB,GACxC,AAAC,UAAOF,QAAQC,GAAG,CAACC,kBAAkB,GACtC;IAEJ,qBACE;kBACGX,SAASY,GAAG,CAAC,CAACC;YACb,MAAMC,OAAO,AAAGjB,UAAUkB,WAAW,GAAC,YAAStB,cAAcoB,SAASL;YACtE,MAAMQ,QAAQH,MAAMI,QAAQ,CAAC;YAC7B,wEAAwE;YACxE,0IAA0I;YAC1I,gDAAgD;YAChD,IAAID,OAAO;gBACT,qBACE,KAACE;oBAEC,aAAa;oBACbC,YAAW;oBACXL,MAAMA;oBACNM,KAAI;oBACJC,IAAG;mBALER;YAQX,OAAO;gBACL,kEAAkE;gBAClEtB,QAAQuB,MAAM;oBACZO,IAAI;oBACJC,eAAe;gBACjB;gBACA,OAAO;YACT;QACF;;AAGN"}