{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/sorted-routes.ts"], "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n"], "names": ["UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "placeholder", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "Map", "getSortedRoutes", "normalizedPages", "root", "pagePath", "getSortedRouteObjects", "objects", "getter", "indexes", "pathnames", "i", "pathname", "sorted"], "mappings": "AAAA,MAAMA;IAOJC,OAAOC,OAAe,EAAQ;QAC5B,IAAI,CAACC,OAAO,CAACD,QAAQE,KAAK,CAAC,KAAKC,MAAM,CAACC,UAAU,EAAE,EAAE;IACvD;IAEAC,SAAmB;QACjB,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQA,QAAQC,MAAoB,EAAY;QAAhCA,IAAAA,mBAAAA,SAAiB;QAC/B,MAAMC,gBAAgB;eAAI,IAAI,CAACC,QAAQ,CAACC,IAAI;SAAG,CAACC,IAAI;QACpD,IAAI,IAAI,CAACC,QAAQ,KAAK,MAAM;YAC1BJ,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,OAAO;QACpD;QACA,IAAI,IAAI,CAACC,YAAY,KAAK,MAAM;YAC9BP,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,UAAU;QACvD;QACA,IAAI,IAAI,CAACE,oBAAoB,KAAK,MAAM;YACtCR,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,YAAY;QACzD;QAEA,MAAMG,SAAST,cACZU,GAAG,CAAC,CAACC,IAAM,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,GAAIb,OAAO,CAAC,AAAC,KAAEC,SAASY,IAAE,MACvDE,MAAM,CAAC,CAACC,MAAMC,OAAS;mBAAID;mBAASC;aAAK,EAAE,EAAE;QAEhD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1BK,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CAACW,GAAG,CAAC,MAAOd,OAAO,CAAC,AAAGC,SAAO,MAAG,IAAI,CAACK,QAAQ,GAAC;QAEnE;QAEA,IAAI,CAAC,IAAI,CAACa,WAAW,EAAE;YACrB,MAAMC,IAAInB,WAAW,MAAM,MAAMA,OAAOoB,KAAK,CAAC,GAAG,CAAC;YAClD,IAAI,IAAI,CAACX,oBAAoB,IAAI,MAAM;gBACrC,MAAM,qBAEL,CAFK,IAAIY,MACR,AAAC,yFAAsFF,IAAE,YAASA,IAAE,UAAO,IAAI,CAACV,oBAAoB,GAAC,UADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAC,OAAOY,OAAO,CAACH;QACjB;QAEA,IAAI,IAAI,CAACX,YAAY,KAAK,MAAM;YAC9BE,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,SACJd,OAAO,CAAC,AAAGC,SAAO,SAAM,IAAI,CAACQ,YAAY,GAAC;QAEjD;QAEA,IAAI,IAAI,CAACC,oBAAoB,KAAK,MAAM;YACtCC,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,WACJd,OAAO,CAAC,AAAGC,SAAO,UAAO,IAAI,CAACS,oBAAoB,GAAC;QAE1D;QAEA,OAAOC;IACT;IAEQhB,QACN6B,QAAkB,EAClBC,SAAmB,EACnBC,UAAmB,EACb;QACN,IAAIF,SAASG,MAAM,KAAK,GAAG;YACzB,IAAI,CAACR,WAAW,GAAG;YACnB;QACF;QAEA,IAAIO,YAAY;YACd,MAAM,qBAAwD,CAAxD,IAAIJ,MAAO,gDAAX,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,wCAAwC;QACxC,IAAIM,cAAcJ,QAAQ,CAAC,EAAE;QAE7B,6CAA6C;QAC7C,IAAII,YAAYC,UAAU,CAAC,QAAQD,YAAYE,QAAQ,CAAC,MAAM;YAC5D,8CAA8C;YAC9C,IAAIC,cAAcH,YAAYP,KAAK,CAAC,GAAG,CAAC;YAExC,IAAIW,aAAa;YACjB,IAAID,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,uDAAuD;gBACvDC,cAAcA,YAAYV,KAAK,CAAC,GAAG,CAAC;gBACpCW,aAAa;YACf;YAEA,IAAID,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,qBAEL,CAFK,IAAIP,MACR,AAAC,+CAA4CS,cAAY,8BADrD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,QAAQ;gBACjC,wCAAwC;gBACxCE,cAAcA,YAAYE,SAAS,CAAC;gBACpCP,aAAa;YACf;YAEA,IAAIK,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,MAAM,qBAEL,CAFK,IAAIR,MACR,AAAC,8DAA2DS,cAAY,QADpE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,qBAEL,CAFK,IAAIP,MACR,AAAC,0DAAuDS,cAAY,QADhE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,SAASG,WAAWC,YAA2B,EAAEC,QAAgB;gBAC/D,IAAID,iBAAiB,MAAM;oBACzB,6EAA6E;oBAC7E,iCAAiC;oBACjC,wBAAwB;oBACxB,sBAAsB;oBACtB,wFAAwF;oBACxF,IAAIA,iBAAiBC,UAAU;wBAC7B,wHAAwH;wBACxH,MAAM,qBAEL,CAFK,IAAId,MACR,AAAC,qEAAkEa,eAAa,YAASC,WAAS,QAD9F,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUY,OAAO,CAAC,CAACC;oBACjB,IAAIA,SAASF,UAAU;wBACrB,MAAM,qBAEL,CAFK,IAAId,MACR,AAAC,yCAAsCc,WAAS,0CAD5C,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIE,KAAKC,OAAO,CAAC,OAAO,QAAQX,YAAYW,OAAO,CAAC,OAAO,KAAK;wBAC9D,MAAM,qBAEL,CAFK,IAAIjB,MACR,AAAC,qCAAkCgB,OAAK,YAASF,WAAS,mEADtD,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUP,IAAI,CAACkB;YACjB;YAEA,IAAIV,YAAY;gBACd,IAAIM,YAAY;oBACd,IAAI,IAAI,CAACvB,YAAY,IAAI,MAAM;wBAC7B,MAAM,qBAEL,CAFK,IAAIa,MACR,AAAC,0FAAuF,IAAI,CAACb,YAAY,GAAC,aAAUe,QAAQ,CAAC,EAAE,GAAC,SAD5H,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACxB,oBAAoB,EAAEqB;oBACtC,6DAA6D;oBAC7D,IAAI,CAACrB,oBAAoB,GAAGqB;oBAC5B,oFAAoF;oBACpFH,cAAc;gBAChB,OAAO;oBACL,IAAI,IAAI,CAAClB,oBAAoB,IAAI,MAAM;wBACrC,MAAM,qBAEL,CAFK,IAAIY,MACR,AAAC,2FAAwF,IAAI,CAACZ,oBAAoB,GAAC,cAAWc,QAAQ,CAAC,EAAE,GAAC,QADtI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACzB,YAAY,EAAEsB;oBAC9B,6DAA6D;oBAC7D,IAAI,CAACtB,YAAY,GAAGsB;oBACpB,kFAAkF;oBAClFH,cAAc;gBAChB;YACF,OAAO;gBACL,IAAII,YAAY;oBACd,MAAM,qBAEL,CAFK,IAAIV,MACR,AAAC,uDAAoDE,QAAQ,CAAC,EAAE,GAAC,QAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACAU,WAAW,IAAI,CAAC5B,QAAQ,EAAEyB;gBAC1B,6DAA6D;gBAC7D,IAAI,CAACzB,QAAQ,GAAGyB;gBAChB,+EAA+E;gBAC/EH,cAAc;YAChB;QACF;QAEA,iFAAiF;QACjF,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACqC,GAAG,CAACZ,cAAc;YACnC,IAAI,CAACzB,QAAQ,CAACsC,GAAG,CAACb,aAAa,IAAIpC;QACrC;QAEA,IAAI,CAACW,QAAQ,CACVW,GAAG,CAACc,aACJjC,OAAO,CAAC6B,SAASH,KAAK,CAAC,IAAII,WAAWC;IAC3C;;aAvMAP,cAAuB;aACvBhB,WAAiC,IAAIuC;aACrCpC,WAA0B;aAC1BG,eAA8B;aAC9BC,uBAAsC;;AAoMxC;AAEA,OAAO,SAASiC,gBACdC,eAAsC;IAEtC,kFAAkF;IAClF,4EAA4E;IAC5E,2CAA2C;IAE3C,yEAAyE;IACzE,2BAA2B;IAC3B,oCAAoC;IACpC,8EAA8E;IAC9E,wEAAwE;IACxE,gHAAgH;IAChH,4EAA4E;IAC5E,MAAMC,OAAO,IAAIrD;IAEjB,6FAA6F;IAC7FoD,gBAAgBP,OAAO,CAAC,CAACS,WAAaD,KAAKpD,MAAM,CAACqD;IAClD,4GAA4G;IAC5G,OAAOD,KAAK9C,MAAM;AACpB;AAEA,OAAO,SAASgD,sBACdC,OAAY,EACZC,MAA0B;IAE1B,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMC,UAAkC,CAAC;IACzC,MAAMC,YAAsB,EAAE;IAC9B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQrB,MAAM,EAAEyB,IAAK;QACvC,MAAMC,WAAWJ,OAAOD,OAAO,CAACI,EAAE;QAClCF,OAAO,CAACG,SAAS,GAAGD;QACpBD,SAAS,CAACC,EAAE,GAAGC;IACjB;IAEA,sBAAsB;IACtB,MAAMC,SAASX,gBAAgBQ;IAE/B,6EAA6E;IAC7E,SAAS;IACT,OAAOG,OAAO1C,GAAG,CAAC,CAACyC,WAAaL,OAAO,CAACE,OAAO,CAACG,SAAS,CAAC;AAC5D"}