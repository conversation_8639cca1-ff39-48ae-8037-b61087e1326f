{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactD<PERSON> from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg:\n    | 'react-server-dom-turbopack/server.edge'\n    | 'react-server-dom-turbopack/server.node'\n    | 'react-server-dom-turbopack/static.edge'\n    | 'react-server-dom-webpack/server.edge'\n    | 'react-server-dom-webpack/server.node'\n    | 'react-server-dom-webpack/static.edge'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackServerEdge, ReactServerDOMWebpackServerEdge\nlet ReactServerDOMTurbopackServerNode, ReactServerDOMWebpackServerNode\nlet ReactServerDOMTurbopackStaticEdge, ReactServerDOMWebpackStaticEdge\n\nif (process.env.TURBOPACK) {\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMTurbopackServerEdge = require('react-server-dom-turbopack/server.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackServerEdge = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/server.edge'\n    )\n  }\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMTurbopackServerNode = require('react-server-dom-turbopack/server.node')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackServerNode = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/server.node'\n    )\n  }\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMTurbopackStaticEdge = require('react-server-dom-turbopack/static.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackStaticEdge = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/static.edge'\n    )\n  }\n} else {\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMWebpackServerEdge = require('react-server-dom-webpack/server.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackServerEdge = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/server.edge'\n    )\n  }\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMWebpackServerNode = require('react-server-dom-webpack/server.node')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackServerNode = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/server.node'\n    )\n  }\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMWebpackStaticEdge = require('react-server-dom-webpack/static.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackStaticEdge = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/static.edge'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactServerDOMWebpackServerEdge,\n  ReactServerDOMTurbopackServerEdge,\n  ReactServerDOMWebpackServerNode,\n  ReactServerDOMTurbopackServerNode,\n  ReactServerDOMWebpackStaticEdge,\n  ReactServerDOMTurbopackStaticEdge,\n}\n"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactCompilerRuntime", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackServerEdge", "ReactServerDOMWebpackServerEdge", "ReactServerDOMTurbopackServerNode", "ReactServerDOMWebpackServerNode", "ReactServerDOMTurbopackStaticEdge", "ReactServerDOMWebpackStaticEdge", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,YAAW;AACrC,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AACpD,YAAYC,0BAA0B,yBAAwB;AAE9D,SAASC,0BACPC,IAA6B,EAC7BC,GAM0C;IAE1C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIS,mCAAmCC;AACvC,IAAIC,mCAAmCC;AACvC,IAAIC,mCAAmCC;AAEvC,IAAIlB,QAAQC,GAAG,CAACkB,SAAS,EAAE;IACzB,6DAA6D;IAC7DN,oCAAoCO,QAAQ;IAC5C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,kCAAkCjB,0BAChC,aACA;IAEJ;IACA,6DAA6D;IAC7DkB,oCAAoCK,QAAQ;IAC5C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Cc,kCAAkCnB,0BAChC,aACA;IAEJ;IACA,6DAA6D;IAC7DoB,oCAAoCG,QAAQ;IAC5C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CgB,kCAAkCrB,0BAChC,aACA;IAEJ;AACF,OAAO;IACL,6DAA6D;IAC7DiB,kCAAkCM,QAAQ;IAC1C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,oCAAoChB,0BAClC,WACA;IAEJ;IACA,6DAA6D;IAC7DmB,kCAAkCI,QAAQ;IAC1C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Ca,oCAAoClB,0BAClC,WACA;IAEJ;IACA,6DAA6D;IAC7DqB,kCAAkCE,QAAQ;IAC1C,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Ce,oCAAoCpB,0BAClC,WACA;IAEJ;AACF;AAEA,SACEL,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfC,oBAAoB,EACpBH,QAAQ,EACRqB,+BAA+B,EAC/BD,iCAAiC,EACjCG,+BAA+B,EAC/BD,iCAAiC,EACjCG,+BAA+B,EAC/BD,iCAAiC,KAClC"}