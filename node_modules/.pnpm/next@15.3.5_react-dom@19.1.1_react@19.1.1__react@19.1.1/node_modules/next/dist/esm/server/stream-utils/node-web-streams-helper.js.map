{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["getTracer", "AppRenderSpan", "Detached<PERSON>romise", "scheduleImmediate", "atLeastOneTask", "ENCODED_TAGS", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "MISSING_ROOT_TAGS_ERROR", "voidCatch", "encoder", "TextEncoder", "chainStreams", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "streamFromString", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "streamFromBuffer", "chunk", "streamToBuffer", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "streamToString", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "createBufferedTransformStream", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "hasBytes", "insertion", "encodedInsertion", "index", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "BODY", "HTML", "createRootLayoutValidatorStream", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "chainTransformers", "transformers", "transformer", "pipeThrough", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "continueDynamicPrerender", "prerenderStream", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "createDocumentClosingStream"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,sBAAqB;AACvE,SAASC,YAAY,QAAQ,gBAAe;AAC5C,SACEC,iBAAiB,EACjBC,uBAAuB,EACvBC,oBAAoB,QACf,uBAAsB;AAC7B,SAASC,uBAAuB,QAAQ,oCAAmC;AAE3E,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEpB,OAAO,SAASC,aACd,GAAGC,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,qBAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACjB;IAEd,OAAOO;AACT;AAEA,OAAO,SAASW,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,OAAO,SAASC,iBAAiBC,KAAa;IAC5C,OAAO,IAAIP,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACI;YACnBL,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,OAAO,eAAeG,eACpBC,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEA,OAAO,eAAeO,eACpBV,MAAkC,EAClCW,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMjB,SAASE,OAAQ;QAChC,IAAIW,0BAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACnB,OAAO;YAAEE,QAAQ;QAAK;IACjD;IAEAe,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEA,OAAO,SAASG;IAId,IAAIC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,yDAAyD;QACzD,IAAI4B,SAAS;QAEb,MAAME,WAAW,IAAI5D;QACrB0D,UAAUE;QAEV3D,kBAAkB;YAChB,IAAI;gBACF,MAAMkC,QAAQ,IAAI0B,WAAWJ;gBAC7B,IAAIK,cAAc;gBAElB,IAAK,IAAIzC,IAAI,GAAGA,IAAImC,eAAe3C,MAAM,EAAEQ,IAAK;oBAC9C,MAAM0C,gBAAgBP,cAAc,CAACnC,EAAE;oBACvCc,MAAM6B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5ET,eAAe3C,MAAM,GAAG;gBACxB4C,mBAAmB;gBACnB3B,WAAWC,OAAO,CAACI;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRuB,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,kDAAkD;YAClD0B,eAAeZ,IAAI,CAACT;YACpBsB,oBAAoBtB,MAAM8B,UAAU;YAEpC,sCAAsC;YACtCN,MAAM7B;QACR;QACA6B;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQxC,OAAO;QACxB;IACF;AACF;AAEA,OAAO,SAASmD,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAO1E,YAAY2E,KAAK,CAAC1E,cAAc2E,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI7D,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/BgD,WAAW;YAEX,MAAMC,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZ,IAAIE,WAAW;oBACb,MAAMC,mBAAmBvE,QAAQuB,MAAM,CAAC+C;oBACxCjD,WAAWC,OAAO,CAACiD;gBACrB;gBACAlD,WAAWC,OAAO,CAACI;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAM8C,QAAQ7E,kBAAkB+B,OAAOhC,aAAa+E,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIF,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmBvE,QAAQuB,MAAM,CAAC+C;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMK,sBAAsB,IAAIvB,WAC9B1B,MAAMtB,MAAM,GAAGmE,iBAAiBnE,MAAM;wBAExC,0DAA0D;wBAC1DuE,oBAAoBpB,GAAG,CAAC7B,MAAMkD,KAAK,CAAC,GAAGJ;wBACvC,qCAAqC;wBACrCG,oBAAoBpB,GAAG,CAACgB,kBAAkBC;wBAC1C,+BAA+B;wBAC/BG,oBAAoBpB,GAAG,CACrB7B,MAAMkD,KAAK,CAACJ,QACZA,QAAQD,iBAAiBnE,MAAM;wBAEjCiB,WAAWC,OAAO,CAACqD;oBACrB,OAAO;wBACLtD,WAAWC,OAAO,CAACI;oBACrB;oBACA0C,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIE,WAAW;wBACbjD,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC+C;oBACpC;oBACAjD,WAAWC,OAAO,CAACI;oBACnB0C,WAAW;gBACb;YACF;QACF;QACA,MAAMlB,OAAM7B,UAAU;YACpB,gEAAgE;YAChE,IAAIgD,UAAU;gBACZ,MAAMC,YAAY,MAAMH;gBACxB,IAAIG,WAAW;oBACbjD,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC+C;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASO,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAI9B;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,MAAM8B,WAAW,IAAI5D;QACrB0D,UAAUE;QAEV3D,kBAAkB;YAChB,IAAI;gBACF6B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACuD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACR7B,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,wCAAwC;YACxC,IAAIqD,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACV7B,MAAM7B;QACR;QACA6B,OAAM7B,UAAU;YACd,IAAI4B,SAAS,OAAOA,QAAQxC,OAAO;YACnC,IAAIsE,SAAS;YAEb,aAAa;YACb1D,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACuD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPpD,MAAkC;IAElC,IAAIqD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa9D,UAA4C;QACtE,IAAI4D,MAAM;YACR;QACF;QAEA,MAAMpD,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMrC;QAEN,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEuC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRkD,cAAc;oBACd;gBACF;gBAEA7D,WAAWC,OAAO,CAACW;YACrB;QACF,EAAE,OAAOmD,KAAK;YACZ/D,WAAWgE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI5E,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,6DAA6D;YAC7D,IAAI,CAACuD,MAAM;gBACTA,OAAOE,aAAa9D;YACtB;QACF;QACA6B,OAAM7B,UAAU;YACd,IAAI6D,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa9D;QAC9B;IACF;AACF;AAEA,MAAMiE,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIhF,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,IAAImE,aAAa;gBACf,OAAOnE,WAAWC,OAAO,CAACI;YAC5B;YAEA,MAAM8C,QAAQ7E,kBAAkB+B,OAAOhC,aAAa+E,MAAM,CAACgB,aAAa;YACxE,IAAIjB,QAAQ,CAAC,GAAG;gBACdgB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAI9D,MAAMtB,MAAM,KAAKV,aAAa+E,MAAM,CAACgB,aAAa,CAACrF,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMsF,SAAShE,MAAMkD,KAAK,CAAC,GAAGJ;gBAC9BnD,WAAWC,OAAO,CAACoE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIhE,MAAMtB,MAAM,GAAGV,aAAa+E,MAAM,CAACgB,aAAa,CAACrF,MAAM,GAAGoE,OAAO;oBACnE,uCAAuC;oBACvC,MAAMmB,QAAQjE,MAAMkD,KAAK,CACvBJ,QAAQ9E,aAAa+E,MAAM,CAACgB,aAAa,CAACrF,MAAM;oBAElDiB,WAAWC,OAAO,CAACqE;gBACrB;YACF,OAAO;gBACLtE,WAAWC,OAAO,CAACI;YACrB;QACF;QACAwB,OAAM7B,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAAC5B,aAAa+E,MAAM,CAACgB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIpF,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACEzB,wBAAwB8B,OAAOhC,aAAa+E,MAAM,CAACgB,aAAa,KAChE7F,wBAAwB8B,OAAOhC,aAAa+E,MAAM,CAACoB,IAAI,KACvDjG,wBAAwB8B,OAAOhC,aAAa+E,MAAM,CAACqB,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFpE,QAAQ7B,qBAAqB6B,OAAOhC,aAAa+E,MAAM,CAACoB,IAAI;YAC5DnE,QAAQ7B,qBAAqB6B,OAAOhC,aAAa+E,MAAM,CAACqB,IAAI;YAE5DzE,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASqE;IAId,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAIzF,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC2E,aACDrG,kBAAkB+B,OAAOhC,aAAawG,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDtG,kBAAkB+B,OAAOhC,aAAawG,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA5E,WAAWC,OAAO,CAACI;QACrB;QACAwB,OAAM7B,UAAU;YACd,MAAM8E,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYhE,IAAI,CAAC;YACjC,IAAI,CAAC8D,WAAWE,YAAYhE,IAAI,CAAC;YAEjC,IAAI,CAACgE,YAAY/F,MAAM,EAAE;YAEzBiB,WAAWC,OAAO,CAChBtB,QAAQuB,MAAM,CACZ,CAAC;;+CAEoC,EAAE4E,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAY/F,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,EAAEN,wBAAwB;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAASyG,kBACPjG,QAA2B,EAC3BkG,YAAyD;IAEzD,IAAI5E,SAAStB;IACb,KAAK,MAAMmG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB7E,SAASA,OAAO8E,WAAW,CAACD;IAC9B;IACA,OAAO7E;AACT;AAcA,OAAO,eAAe+E,mBACpBC,YAAiC,EACjC,EACE9B,MAAM,EACN+B,iBAAiB,EACjBC,kBAAkB,EAClBC,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBpC,SAASA,OAAOqC,KAAK,CAAC7B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIwB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOb,kBAAkBK,cAAc;QACrC,qDAAqD;QACrD9D;QAEA,4BAA4B;QAC5BoB,mCAAmC8C;QAEnC,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAe9G,MAAM,GAAG,IAC9CyE,2BAA2BqC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB7B,4BAA4B6B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBlB,oCAAoC;QAEzD,kDAAkD;QAClDR;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ErB,mCAAmC6C;KACpC;AACH;AAOA,OAAO,eAAeM,yBACpBC,eAA2C,EAC3C,EACEP,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEM,eACE,qDAAqD;KACpDZ,WAAW,CAAC5D,iCACZ4D,WAAW,CAACd,0CACb,gCAAgC;KAC/Bc,WAAW,CAACxC,mCAAmC6C,uBAChD,4BAA4B;KAC3BL,WAAW,CACVxC,mCAAmC8C;AAG3C;AAQA,OAAO,eAAeO,wBACpBD,eAA2C,EAC3C,EACET,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACM;IAEjC,OACEM,eACE,qDAAqD;KACpDZ,WAAW,CAAC5D,gCACb,gCAAgC;KAC/B4D,WAAW,CAACxC,mCAAmC6C,uBAChD,oCAAoC;KACnCL,WAAW,CACVxC,mCAAmC8C,2BAErC,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,mBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAQA,OAAO,eAAeiC,0BACpBZ,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,YACE,qDAAqD;KACpDF,WAAW,CAAC5D,gCACb,gCAAgC;KAC/B4D,WAAW,CAACxC,mCAAmC6C,uBAChD,oCAAoC;KACnCL,WAAW,CACVxC,mCAAmC8C,2BAErC,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,mBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAEA,OAAO,SAASkC;IACd,OAAOxG,iBAAiBqE;AAC1B"}