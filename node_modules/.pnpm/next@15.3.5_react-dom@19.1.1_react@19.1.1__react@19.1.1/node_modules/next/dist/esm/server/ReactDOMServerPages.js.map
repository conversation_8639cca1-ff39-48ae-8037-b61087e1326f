{"version": 3, "sources": ["../../src/server/ReactDOMServerPages.js"], "sourcesContent": ["let ReactDOMServer\n\ntry {\n  ReactDOMServer = require('react-dom/server.edge')\n} catch (error) {\n  if (\n    error.code !== 'MODULE_NOT_FOUND' &&\n    error.code !== 'ERR_PACKAGE_PATH_NOT_EXPORTED'\n  ) {\n    throw error\n  }\n  // In React versions without react-dom/server.edge, the browser build works in Node.js.\n  // The Node.js build does not support renderToReadableStream.\n  ReactDOMServer = require('react-dom/server.browser')\n}\n\nmodule.exports = ReactDOMServer\n"], "names": ["ReactDOMServer", "require", "error", "code", "module", "exports"], "mappings": "AAAA,IAAIA;AAEJ,IAAI;IACFA,iBAAiBC,QAAQ;AAC3B,EAAE,OAAOC,OAAO;IACd,IACEA,MAAMC,IAAI,KAAK,sBACfD,MAAMC,IAAI,KAAK,iCACf;QACA,MAAMD;IACR;IACA,uFAAuF;IACvF,6DAA6D;IAC7DF,iBAAiBC,QAAQ;AAC3B;AAEAG,OAAOC,OAAO,GAAGL"}