{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  decodeReplyFromAsyncIterable,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client.edge'\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getHmrRefreshHash,\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n  getDraftModeProviderForCacheScope,\n} from '../app-render/work-unit-async-storage.external'\nimport { runInCleanSnapshot } from '../app-render/clean-async-snapshot.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\nimport { DYNAMIC_EXPIRE } from './constants'\nimport { getCacheHandler } from './handlers'\nimport { UseCacheTimeoutError } from './use-cache-errors'\nimport { createHangingInputAbortSignal } from '../app-render/dynamic-rendering'\nimport {\n  makeErroringExoticSearchParamsForUseCache,\n  type SearchParams,\n} from '../request/search-params'\nimport type { Params } from '../request/params'\nimport React from 'react'\nimport { createLazyResult, isResolvedLazyResult } from '../lib/lazy-result'\n\ntype CacheKeyParts =\n  | [buildId: string, id: string, args: unknown[]]\n  | [buildId: string, id: string, args: unknown[], hmrRefreshHash: string]\n\nexport interface UseCachePageComponentProps {\n  params: Promise<Params>\n  searchParams: Promise<SearchParams>\n  $$isPageComponent: true\n}\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_CACHE\n  ? console.debug.bind(console, 'use-cache:')\n  : undefined\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new Error(\n      'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n    )\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new Error(\n      'A default cacheLife profile must always be provided. This is a bug in Next.js.'\n    )\n  }\n\n  const useCacheOrRequestStore =\n    outerWorkUnitStore?.type === 'request' ||\n    outerWorkUnitStore?.type === 'cache'\n      ? outerWorkUnitStore\n      : undefined\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore: UseCacheStore = {\n    type: 'cache',\n    phase: 'render',\n    implicitTags: outerWorkUnitStore?.implicitTags,\n    revalidate: defaultCacheLife.revalidate,\n    expire: defaultCacheLife.expire,\n    stale: defaultCacheLife.stale,\n    explicitRevalidate: undefined,\n    explicitExpire: undefined,\n    explicitStale: undefined,\n    tags: null,\n    hmrRefreshHash:\n      outerWorkUnitStore && getHmrRefreshHash(workStore, outerWorkUnitStore),\n    isHmrRefresh: useCacheOrRequestStore?.isHmrRefresh ?? false,\n    serverComponentsHmrCache: useCacheOrRequestStore?.serverComponentsHmrCache,\n    forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n    draftMode:\n      outerWorkUnitStore &&\n      getDraftModeProviderForCacheScope(workStore, outerWorkUnitStore),\n  }\n\n  return workUnitAsyncStorage.run(\n    cacheStore,\n    generateCacheEntryImpl,\n    workStore,\n    outerWorkUnitStore,\n    cacheStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction propagateCacheLifeAndTags(\n  workUnitStore: WorkUnitStore | undefined,\n  entry: CacheEntry\n): void {\n  if (\n    workUnitStore &&\n    (workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy')\n  ) {\n    // Propagate tags and revalidate upwards\n    const outerTags = workUnitStore.tags ?? (workUnitStore.tags = [])\n    const entryTags = entry.tags\n    for (let i = 0; i < entryTags.length; i++) {\n      const tag = entryTags[i]\n      if (!outerTags.includes(tag)) {\n        outerTags.push(tag)\n      }\n    }\n    if (workUnitStore.stale > entry.stale) {\n      workUnitStore.stale = entry.stale\n    }\n    if (workUnitStore.revalidate > entry.revalidate) {\n      workUnitStore.revalidate = entry.revalidate\n    }\n    if (workUnitStore.expire > entry.expire) {\n      workUnitStore.expire = entry.expire\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown>, // This is a live array that gets pushed into.,\n  timer: any\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n  for (let entry; !(entry = await reader.read()).done; ) {\n    buffer.push(entry.value)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (workStore.invalidUsageError) {\n        controller.error(workStore.invalidUsageError)\n      } else if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry: CacheEntry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n  // Propagate tags/revalidate to the parent context.\n  propagateCacheLifeAndTags(outerWorkUnitStore, entry)\n\n  const cacheSignal =\n    outerWorkUnitStore && outerWorkUnitStore.type === 'prerender'\n      ? outerWorkUnitStore.cacheSignal\n      : null\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  if (timer !== undefined) {\n    clearTimeout(timer)\n  }\n\n  return entry\n}\n\nasync function generateCacheEntryImpl(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n\n  const [, , args] =\n    typeof encodedArguments === 'string'\n      ? await decodeReply<CacheKeyParts>(\n          encodedArguments,\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n      : await decodeReplyFromAsyncIterable<CacheKeyParts>(\n          {\n            async *[Symbol.asyncIterator]() {\n              for (const entry of encodedArguments) {\n                yield entry\n              }\n\n              // The encoded arguments might contain hanging promises. In this\n              // case we don't want to reject with \"Error: Connection closed.\",\n              // so we intentionally keep the iterable alive. This is similar to\n              // the halting trick that we do while rendering.\n              if (outerWorkUnitStore?.type === 'prerender') {\n                await new Promise<void>((resolve) => {\n                  if (outerWorkUnitStore.renderSignal.aborted) {\n                    resolve()\n                  } else {\n                    outerWorkUnitStore.renderSignal.addEventListener(\n                      'abort',\n                      () => resolve(),\n                      { once: true }\n                    )\n                  }\n                })\n              }\n            },\n          },\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n\n  // Track the timestamp when we started computing the result.\n  const startTime = performance.timeOrigin + performance.now()\n\n  // Invoke the inner function to load a new result. We delay the invocation\n  // though, until React awaits the promise so that React's request store (ALS)\n  // is available when the function is invoked. This allows us, for example, to\n  // capture logs so that we can later replay them.\n  const resultPromise = createLazyResult(() => fn.apply(null, args))\n\n  let errors: Array<unknown> = []\n\n  let timer = undefined\n  const controller = new AbortController()\n  if (outerWorkUnitStore?.type === 'prerender') {\n    // If we're prerendering, we give you 50 seconds to fill a cache entry.\n    // Otherwise we assume you stalled on hanging input and de-opt. This needs\n    // to be lower than just the general timeout of 60 seconds.\n    timer = setTimeout(() => {\n      controller.abort(timeoutError)\n    }, 50000)\n  }\n\n  const stream = renderToReadableStream(\n    resultPromise,\n    clientReferenceManifest.clientModules,\n    {\n      environmentName: 'Cache',\n      signal: controller.signal,\n      temporaryReferences,\n      // In the \"Cache\" environment, we only need to make sure that the error\n      // digests are handled correctly. Error formatting and reporting is not\n      // necessary here; the errors are encoded in the stream, and will be\n      // reported in the \"Server\" environment.\n      onError: (error) => {\n        const digest = getDigestForWellKnownError(error)\n\n        if (digest) {\n          return digest\n        }\n\n        if (process.env.NODE_ENV !== 'development') {\n          // TODO: For now we're also reporting the error here, because in\n          // production, the \"Server\" environment will only get the obfuscated\n          // error (created by the Flight Client in the cache wrapper).\n          console.error(error)\n        }\n\n        if (error === timeoutError) {\n          // The timeout error already aborted the whole stream. We don't need\n          // to also push this error into the `errors` array.\n          return timeoutError.digest\n        }\n\n        errors.push(error)\n      },\n    }\n  )\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const promiseOfCacheEntry = collectResult(\n    savedStream,\n    workStore,\n    outerWorkUnitStore,\n    innerCacheStore,\n    startTime,\n    errors,\n    timer\n  )\n\n  // Return the stream as we're creating it. This means that if it ends up\n  // erroring we cannot return a stale-while-error version but it allows\n  // streaming back the result earlier.\n  return [returnStream, promiseOfCacheEntry]\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  originalFn: (...args: unknown[]) => Promise<unknown>\n) {\n  const cacheHandler = getCacheHandler(kind)\n  if (cacheHandler === undefined) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n\n  // Capture the timeout error here to ensure a useful stack.\n  const timeoutError = new UseCacheTimeoutError()\n  Error.captureStackTrace(timeoutError, cache)\n\n  const name = originalFn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      let fn = originalFn\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      // In dev mode, when the HMR refresh hash is set, we include it in the\n      // cache key. This ensures that cache entries are not reused when server\n      // components have been edited. This is a very coarse approach. But it's\n      // also only a temporary solution until Action IDs are unique per\n      // implementation. Remove this once Action IDs hash the implementation.\n      const hmrRefreshHash =\n        workUnitStore && getHmrRefreshHash(workStore, workUnitStore)\n\n      const hangingInputAbortSignal =\n        workUnitStore?.type === 'prerender'\n          ? createHangingInputAbortSignal(workUnitStore)\n          : undefined\n\n      // When dynamicIO is not enabled, we can not encode searchParams as\n      // hanging promises. To still avoid unused search params from making a\n      // page dynamic, we overwrite them here with a promise that resolves to an\n      // empty object, while also overwriting the to-be-invoked function for\n      // generating a cache entry with a function that creates an erroring\n      // searchParams prop before invoking the original function. This ensures\n      // that used searchParams inside of cached functions would still yield an\n      // error.\n      if (!workStore.dynamicIOEnabled && isPageComponent(args)) {\n        const [{ params, searchParams }] = args\n        // Overwrite the props to omit $$isPageComponent.\n        args = [{ params, searchParams }]\n\n        fn = {\n          [name]: async ({\n            params: serializedParams,\n          }: Omit<UseCachePageComponentProps, '$$isPageComponent'>) =>\n            originalFn.apply(null, [\n              {\n                params: serializedParams,\n                searchParams:\n                  makeErroringExoticSearchParamsForUseCache(workStore),\n              },\n            ]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n\n      const cacheKeyParts: CacheKeyParts = hmrRefreshHash\n        ? [buildId, id, args, hmrRefreshHash]\n        : [buildId, id, args]\n\n      const encodedCacheKeyParts: FormData | string = await encodeReply(\n        cacheKeyParts,\n        { temporaryReferences, signal: hangingInputAbortSignal }\n      )\n\n      const serializedCacheKey =\n        typeof encodedCacheKeyParts === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedCacheKeyParts\n          : await encodeFormData(encodedCacheKeyParts)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(workUnitStore, existingEntry)\n          if (\n            workUnitStore !== undefined &&\n            workUnitStore.type === 'prerender' &&\n            existingEntry !== undefined &&\n            (existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE)\n          ) {\n            // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n            // expire time is under 5 minutes, then we consider this cache entry dynamic\n            // as it's not worth generating static pages for such data. It's better to leave\n            // a PPR hole that can be filled in dynamically with a potentially cached entry.\n            if (cacheSignal) {\n              cacheSignal.endRead()\n            }\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const lazyRefreshTags = workStore.refreshTagsByCacheKind.get(kind)\n\n        if (lazyRefreshTags && !isResolvedLazyResult(lazyRefreshTags)) {\n          await lazyRefreshTags\n        }\n\n        let entry = shouldForceRevalidate(workStore, workUnitStore)\n          ? undefined\n          : 'getExpiration' in cacheHandler\n            ? await cacheHandler.get(serializedCacheKey)\n            : // Legacy cache handlers require implicit tags to be passed in,\n              // instead of checking their staleness here, as we do for modern\n              // cache handlers (see below).\n              await cacheHandler.get(\n                serializedCacheKey,\n                workUnitStore?.implicitTags?.tags ?? []\n              )\n\n        if (entry) {\n          const implicitTags = workUnitStore?.implicitTags?.tags ?? []\n          let implicitTagsExpiration = 0\n\n          if (workUnitStore?.implicitTags) {\n            const lazyExpiration =\n              workUnitStore.implicitTags.expirationsByCacheKind.get(kind)\n\n            if (lazyExpiration) {\n              if (isResolvedLazyResult(lazyExpiration)) {\n                implicitTagsExpiration = lazyExpiration.value\n              } else {\n                implicitTagsExpiration = await lazyExpiration\n              }\n            }\n          }\n\n          if (\n            shouldDiscardCacheEntry(\n              entry,\n              workStore,\n              implicitTags,\n              implicitTagsExpiration\n            )\n          ) {\n            debug?.('discarding stale entry', serializedCacheKey)\n            entry = undefined\n          }\n        }\n\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender' &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n          // expire time is under 5 minutes, then we consider this cache entry dynamic\n          // as it's not worth generating static pages for such data. It's better to leave\n          // a PPR hole that can be filled in dynamically with a potentially cached entry.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            'dynamic \"use cache\"'\n          )\n        } else if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          if (entry) {\n            if (currentTime > entry.timestamp + entry.expire * 1000) {\n              debug?.('entry is expired', serializedCacheKey)\n            }\n\n            if (\n              workStore.isStaticGeneration &&\n              currentTime > entry.timestamp + entry.revalidate * 1000\n            ) {\n              debug?.('static generation, entry is stale', serializedCacheKey)\n            }\n          }\n\n          const [newStream, pendingCacheEntry] = await generateCacheEntry(\n            workStore,\n            workUnitStore,\n            clientReferenceManifest,\n            encodedCacheKeyParts,\n            fn,\n            timeoutError\n          )\n\n          // When draft mode is enabled, we must not save the cache entry.\n          if (!workStore.isDraftMode) {\n            let savedCacheEntry\n\n            if (prerenderResumeDataCache) {\n              // Create a clone that goes into the cache scope memory cache.\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            workStore.pendingRevalidateWrites ??= []\n            workStore.pendingRevalidateWrites.push(promise)\n          }\n\n          stream = newStream\n        } else {\n          propagateCacheLifeAndTags(workUnitStore, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is dynamic render),\n            // then we should warm up the cache with a fresh revalidated entry.\n            const [ignoredStream, pendingCacheEntry] = await generateCacheEntry(\n              workStore,\n              undefined, // This is not running within the context of this unit.\n              clientReferenceManifest,\n              encodedCacheKeyParts,\n              fn,\n              timeoutError\n            )\n\n            let savedCacheEntry: Promise<CacheEntry>\n            if (prerenderResumeDataCache) {\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            if (!workStore.pendingRevalidateWrites) {\n              workStore.pendingRevalidateWrites = []\n            }\n            workStore.pendingRevalidateWrites.push(promise)\n\n            await ignoredStream.cancel()\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n\n  return React.cache(cachedFn)\n}\n\nfunction isPageComponent(\n  args: any[]\n): args is [UseCachePageComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCachePageComponentProps).$$isPageComponent\n  )\n}\n\nfunction shouldForceRevalidate(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined\n): boolean {\n  if (workStore.isOnDemandRevalidate || workStore.isDraftMode) {\n    return true\n  }\n\n  if (workStore.dev && workUnitStore) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.headers.get('cache-control') === 'no-cache'\n    }\n\n    if (workUnitStore.type === 'cache') {\n      return workUnitStore.forceRevalidate\n    }\n  }\n\n  return false\n}\n\nfunction shouldDiscardCacheEntry(\n  entry: CacheEntry,\n  workStore: WorkStore,\n  implicitTags: string[],\n  implicitTagsExpiration: number\n): boolean {\n  // If the cache entry contains revalidated tags that the cache handler might\n  // not know about yet, we need to discard it.\n  if (entry.tags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  // If the cache entry was created before any of the implicit tags were\n  // revalidated last, we also need to discard it.\n  if (entry.timestamp <= implicitTagsExpiration) {\n    debug?.(\n      'entry was created at',\n      entry.timestamp,\n      'before implicit tags were revalidated at',\n      implicitTagsExpiration\n    )\n\n    return true\n  }\n\n  // Finally, if any of the implicit tags have been revalidated recently, we\n  // also need to discard the cache entry.\n  if (implicitTags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  return false\n}\n\nfunction isRecentlyRevalidatedTag(tag: string, workStore: WorkStore): boolean {\n  const { previouslyRevalidatedTags, pendingRevalidatedTags } = workStore\n\n  // Was the tag previously revalidated (e.g. by a redirecting server action)?\n  if (previouslyRevalidatedTags.includes(tag)) {\n    debug?.('tag', tag, 'was previously revalidated')\n\n    return true\n  }\n\n  // It could also have been revalidated by the currently running server action.\n  // In this case the revalidation might not have been propagated to the cache\n  // handler yet, so we read it from the pending tags in the work store.\n  if (pendingRevalidatedTags?.includes(tag)) {\n    debug?.('tag', tag, 'was just revalidated')\n\n    return true\n  }\n\n  return false\n}\n"], "names": ["renderToReadableStream", "decodeReply", "decodeReplyFromAsyncIterable", "createTemporaryReferenceSet", "createServerTemporaryReferenceSet", "createFromReadableStream", "encodeReply", "createClientTemporaryReferenceSet", "workAsyncStorage", "getHmrRefreshHash", "getRenderResumeDataCache", "getPrerenderResumeDataCache", "workUnitAsyncStorage", "getDraftModeProviderForCacheScope", "runInCleanSnapshot", "makeHangingPromise", "getClientReferenceManifestForRsc", "getServerModuleMap", "decryptActionBoundArgs", "InvariantError", "getDigestForWellKnownError", "DYNAMIC_EXPIRE", "getCache<PERSON><PERSON><PERSON>", "UseCacheTimeoutError", "createHangingInputAbortSignal", "makeErroringExoticSearchParamsForUseCache", "React", "createLazyResult", "isResolvedLazyResult", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "console", "bind", "undefined", "generateCacheEntry", "workStore", "outerWorkUnitStore", "clientReferenceManifest", "encodedArguments", "fn", "timeoutError", "generateCacheEntryWithRestoredWorkStore", "run", "generateCacheEntryWithCacheContext", "cacheLifeProfiles", "Error", "defaultCacheLife", "revalidate", "expire", "stale", "useCacheOrRequestStore", "type", "cacheStore", "phase", "implicitTags", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "hmrRefreshHash", "isHmrRefresh", "serverComponentsHmrCache", "forceRevalidate", "shouldForceRevalidate", "draftMode", "generateCacheEntryImpl", "propagateCacheLifeAndTags", "workUnitStore", "entry", "outerTags", "entryTags", "i", "length", "tag", "includes", "push", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "timer", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "idx", "bufferStream", "ReadableStream", "pull", "controller", "invalidUsageError", "error", "enqueue", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "endRead", "clearTimeout", "temporaryReferences", "args", "Symbol", "asyncIterator", "Promise", "resolve", "renderSignal", "aborted", "addEventListener", "once", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "resultPromise", "apply", "AbortController", "setTimeout", "abort", "stream", "clientModules", "environmentName", "signal", "onError", "digest", "NODE_ENV", "returnStream", "tee", "promiseOfCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "pendingCacheEntry", "getNthCacheEntry", "split", "encodeFormData", "formData", "result", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "cache", "kind", "id", "boundArgs<PERSON><PERSON>th", "originalFn", "cache<PERSON><PERSON><PERSON>", "captureStackTrace", "name", "cachedFn", "getStore", "buildId", "hangingInputAbortSignal", "dynamicIOEnabled", "isPageComponent", "params", "searchParams", "serializedParams", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "Array", "isArray", "unshift", "cacheKeyParts", "encodedCacheKeyParts", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "renderResumeDataCache", "beginRead", "cachedEntry", "get", "existingEntry", "lazyRefreshTags", "refreshTagsByCacheKind", "implicitTagsExpiration", "lazyExpiration", "expirationsByCacheKind", "shouldDiscardCacheEntry", "currentTime", "isStaticGeneration", "newStream", "isDraftMode", "savedCacheEntry", "set", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "props", "ref", "$$isPageComponent", "isOnDemandRevalidate", "dev", "headers", "some", "isRecentlyRevalidatedTag", "previouslyRevalidatedTags", "pendingRevalidatedTags"], "mappings": "AACA,oDAAoD,GACpD,SACEA,sBAAsB,EACtBC,WAAW,EACXC,4BAA4B,EAC5BC,+BAA+BC,iCAAiC,QAC3D,uCAAsC;AAC7C,oDAAoD,GACpD,SACEC,wBAAwB,EACxBC,WAAW,EACXH,+BAA+BI,iCAAiC,QAC3D,uCAAsC;AAG7C,SAASC,gBAAgB,QAAQ,4CAA2C;AAK5E,SACEC,iBAAiB,EACjBC,wBAAwB,EACxBC,2BAA2B,EAC3BC,oBAAoB,EACpBC,iCAAiC,QAC5B,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,8CAA6C;AAEhF,SAASC,kBAAkB,QAAQ,6BAA4B;AAI/D,SACEC,gCAAgC,EAChCC,kBAAkB,QACb,iCAAgC;AAGvC,SAASC,sBAAsB,QAAQ,2BAA0B;AACjE,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,0BAA0B,QAAQ,qCAAoC;AAC/E,SAASC,cAAc,QAAQ,cAAa;AAC5C,SAASC,eAAe,QAAQ,aAAY;AAC5C,SAASC,oBAAoB,QAAQ,qBAAoB;AACzD,SAASC,6BAA6B,QAAQ,kCAAiC;AAC/E,SACEC,yCAAyC,QAEpC,2BAA0B;AAEjC,OAAOC,WAAW,QAAO;AACzB,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,qBAAoB;AAY3E,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,QAAQH,QAAQC,GAAG,CAACG,wBAAwB,GAC9CC,QAAQF,KAAK,CAACG,IAAI,CAACD,SAAS,gBAC5BE;AAEJ,SAASC,mBACPC,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAO9B,mBACL+B,yCACAN,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASC,wCACPN,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAOpC,iBAAiBsC,GAAG,CACzBP,WACAQ,oCACAR,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASG,mCACPR,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,IAAI,CAACL,UAAUS,iBAAiB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,2EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMC,mBAAmBX,UAAUS,iBAAiB,CAAC,UAAU;IAC/D,IACE,CAACE,oBACDA,iBAAiBC,UAAU,IAAI,QAC/BD,iBAAiBE,MAAM,IAAI,QAC3BF,iBAAiBG,KAAK,IAAI,MAC1B;QACA,MAAM,qBAEL,CAFK,IAAIJ,MACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,yBACJd,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAC7Bf,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,UACzBf,qBACAH;IAEN,6CAA6C;IAC7C,MAAMmB,aAA4B;QAChCD,MAAM;QACNE,OAAO;QACPC,YAAY,EAAElB,sCAAAA,mBAAoBkB,YAAY;QAC9CP,YAAYD,iBAAiBC,UAAU;QACvCC,QAAQF,iBAAiBE,MAAM;QAC/BC,OAAOH,iBAAiBG,KAAK;QAC7BM,oBAAoBtB;QACpBuB,gBAAgBvB;QAChBwB,eAAexB;QACfyB,MAAM;QACNC,gBACEvB,sBAAsB/B,kBAAkB8B,WAAWC;QACrDwB,cAAcV,CAAAA,0CAAAA,uBAAwBU,YAAY,KAAI;QACtDC,wBAAwB,EAAEX,0CAAAA,uBAAwBW,wBAAwB;QAC1EC,iBAAiBC,sBAAsB5B,WAAWC;QAClD4B,WACE5B,sBACA3B,kCAAkC0B,WAAWC;IACjD;IAEA,OAAO5B,qBAAqBkC,GAAG,CAC7BU,YACAa,wBACA9B,WACAC,oBACAgB,YACAf,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAAS0B,0BACPC,aAAwC,EACxCC,KAAiB;IAEjB,IACED,iBACCA,CAAAA,cAAchB,IAAI,KAAK,WACtBgB,cAAchB,IAAI,KAAK,eACvBgB,cAAchB,IAAI,KAAK,mBACvBgB,cAAchB,IAAI,KAAK,kBAAiB,GAC1C;QACA,wCAAwC;QACxC,MAAMkB,YAAYF,cAAcT,IAAI,IAAKS,CAAAA,cAAcT,IAAI,GAAG,EAAE,AAAD;QAC/D,MAAMY,YAAYF,MAAMV,IAAI;QAC5B,IAAK,IAAIa,IAAI,GAAGA,IAAID,UAAUE,MAAM,EAAED,IAAK;YACzC,MAAME,MAAMH,SAAS,CAACC,EAAE;YACxB,IAAI,CAACF,UAAUK,QAAQ,CAACD,MAAM;gBAC5BJ,UAAUM,IAAI,CAACF;YACjB;QACF;QACA,IAAIN,cAAclB,KAAK,GAAGmB,MAAMnB,KAAK,EAAE;YACrCkB,cAAclB,KAAK,GAAGmB,MAAMnB,KAAK;QACnC;QACA,IAAIkB,cAAcpB,UAAU,GAAGqB,MAAMrB,UAAU,EAAE;YAC/CoB,cAAcpB,UAAU,GAAGqB,MAAMrB,UAAU;QAC7C;QACA,IAAIoB,cAAcnB,MAAM,GAAGoB,MAAMpB,MAAM,EAAE;YACvCmB,cAAcnB,MAAM,GAAGoB,MAAMpB,MAAM;QACrC;IACF;AACF;AAEA,eAAe4B,cACbC,WAA2B,EAC3B1C,SAAoB,EACpBC,kBAA6C,EAC7C0C,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB,EACtBC,KAAU;IAEV,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASN,YAAYO,SAAS;IACpC,IAAK,IAAIhB,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMe,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;QACrDJ,OAAOP,IAAI,CAACP,MAAMmB,KAAK;IACzB;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAIzD,UAAU0D,iBAAiB,EAAE;gBAC/BD,WAAWE,KAAK,CAAC3D,UAAU0D,iBAAiB;YAC9C,OAAO,IAAIL,MAAMN,OAAOV,MAAM,EAAE;gBAC9BoB,WAAWG,OAAO,CAACb,MAAM,CAACM,MAAM;YAClC,OAAO,IAAIR,OAAOR,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CoB,WAAWE,KAAK,CAACd,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWI,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBnB,gBAAgBpB,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAMwC,sBACJpB,gBAAgBvB,kBAAkB,KAAKtB,YACnC6C,gBAAgBvB,kBAAkB,GAClCuB,gBAAgB/B,UAAU;IAChC,MAAMoD,kBACJrB,gBAAgBtB,cAAc,KAAKvB,YAC/B6C,gBAAgBtB,cAAc,GAC9BsB,gBAAgB9B,MAAM;IAC5B,MAAMoD,iBACJtB,gBAAgBrB,aAAa,KAAKxB,YAC9B6C,gBAAgBrB,aAAa,GAC7BqB,gBAAgB7B,KAAK;IAE3B,MAAMmB,QAAoB;QACxBmB,OAAOE;QACPY,WAAWtB;QACXhC,YAAYmD;QACZlD,QAAQmD;QACRlD,OAAOmD;QACP1C,MAAMuC,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IACA,mDAAmD;IACnD/B,0BAA0B9B,oBAAoBgC;IAE9C,MAAMkC,cACJlE,sBAAsBA,mBAAmBe,IAAI,KAAK,cAC9Cf,mBAAmBkE,WAAW,GAC9B;IACN,IAAIA,aAAa;QACfA,YAAYC,OAAO;IACrB;IAEA,IAAItB,UAAUhD,WAAW;QACvBuE,aAAavB;IACf;IAEA,OAAOb;AACT;AAEA,eAAeH,uBACb9B,SAAoB,EACpBC,kBAA6C,EAC7C0C,eAA8B,EAC9BzC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,MAAMiE,sBAAsBzG;IAE5B,MAAM,KAAK0G,KAAK,GACd,OAAOpE,qBAAqB,WACxB,MAAMzC,YACJyC,kBACAzB,sBACA;QAAE4F;IAAoB,KAExB,MAAM3G,6BACJ;QACE,OAAO,CAAC6G,OAAOC,aAAa,CAAC;YAC3B,KAAK,MAAMxC,SAAS9B,iBAAkB;gBACpC,MAAM8B;YACR;YAEA,gEAAgE;YAChE,iEAAiE;YACjE,kEAAkE;YAClE,gDAAgD;YAChD,IAAIhC,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAAa;gBAC5C,MAAM,IAAI0D,QAAc,CAACC;oBACvB,IAAI1E,mBAAmB2E,YAAY,CAACC,OAAO,EAAE;wBAC3CF;oBACF,OAAO;wBACL1E,mBAAmB2E,YAAY,CAACE,gBAAgB,CAC9C,SACA,IAAMH,WACN;4BAAEI,MAAM;wBAAK;oBAEjB;gBACF;YACF;QACF;IACF,GACArG,sBACA;QAAE4F;IAAoB;IAG9B,4DAA4D;IAC5D,MAAM1B,YAAYoC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAE1D,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,iDAAiD;IACjD,MAAMC,gBAAgB/F,iBAAiB,IAAMgB,GAAGgF,KAAK,CAAC,MAAMb;IAE5D,IAAI1B,SAAyB,EAAE;IAE/B,IAAIC,QAAQhD;IACZ,MAAM2D,aAAa,IAAI4B;IACvB,IAAIpF,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAAa;QAC5C,uEAAuE;QACvE,0EAA0E;QAC1E,2DAA2D;QAC3D8B,QAAQwC,WAAW;YACjB7B,WAAW8B,KAAK,CAAClF;QACnB,GAAG;IACL;IAEA,MAAMmF,SAAS/H,uBACb0H,eACAjF,wBAAwBuF,aAAa,EACrC;QACEC,iBAAiB;QACjBC,QAAQlC,WAAWkC,MAAM;QACzBrB;QACA,uEAAuE;QACvE,uEAAuE;QACvE,oEAAoE;QACpE,wCAAwC;QACxCsB,SAAS,CAACjC;YACR,MAAMkC,SAAShH,2BAA2B8E;YAE1C,IAAIkC,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAItG,QAAQC,GAAG,CAACsG,QAAQ,KAAK,eAAe;gBAC1C,gEAAgE;gBAChE,oEAAoE;gBACpE,6DAA6D;gBAC7DlG,QAAQ+D,KAAK,CAACA;YAChB;YAEA,IAAIA,UAAUtD,cAAc;gBAC1B,oEAAoE;gBACpE,mDAAmD;gBACnD,OAAOA,aAAawF,MAAM;YAC5B;YAEAhD,OAAOL,IAAI,CAACmB;QACd;IACF;IAGF,MAAM,CAACoC,cAAcrD,YAAY,GAAG8C,OAAOQ,GAAG;IAE9C,MAAMC,sBAAsBxD,cAC1BC,aACA1C,WACAC,oBACA0C,iBACAC,WACAC,QACAC;IAGF,wEAAwE;IACxE,sEAAsE;IACtE,qCAAqC;IACrC,OAAO;QAACiD;QAAcE;KAAoB;AAC5C;AAEA,SAASC,gBAAgBjE,KAAiB;IACxC,MAAM,CAACkE,SAASC,QAAQ,GAAGnE,MAAMmB,KAAK,CAAC4C,GAAG;IAC1C/D,MAAMmB,KAAK,GAAG+C;IACd,MAAME,cAA0B;QAC9BjD,OAAOgD;QACPlC,WAAWjC,MAAMiC,SAAS;QAC1BtD,YAAYqB,MAAMrB,UAAU;QAC5BC,QAAQoB,MAAMpB,MAAM;QACpBC,OAAOmB,MAAMnB,KAAK;QAClBS,MAAMU,MAAMV,IAAI;IAClB;IACA,OAAO;QAACU;QAAOoE;KAAY;AAC7B;AAEA,eAAeC,uBACbC,iBAAsC;IAEtC,MAAMtE,QAAQ,MAAMsE;IACpB,OAAOL,gBAAgBjE;AACzB;AAEA,eAAeuE,iBACbC,KAAwC,EACxCrE,CAAS;IAET,OAAO,AAAC,CAAA,MAAMqE,KAAI,CAAE,CAACrE,EAAE;AACzB;AAEA,eAAesE,eAAeC,QAAkB;IAC9C,IAAIC,SAAS;IACb,KAAK,IAAI,CAACC,KAAKzD,MAAM,IAAIuD,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvBC,UAAUC,IAAIxE,MAAM,CAACyE,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAO3D,UAAU,UAAU;YAC7B2D,cAAc3D;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAM4D,cAAc,MAAM5D,MAAM4D,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAL,UAAUG,YAAY1E,MAAM,CAACyE,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAOH;AACT;AAEA,SAASU,4BACP9B,MAAsB,EACtBrB,WAAwB;IAExB,MAAMnB,SAASwC,OAAOvC,SAAS;IAC/B,OAAO,IAAIM,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEN,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRM,WAAWI,KAAK;gBAChBM,YAAYC,OAAO;YACrB,OAAO;gBACLX,WAAWG,OAAO,CAACR;YACrB;QACF;IACF;AACF;AAEA,OAAO,SAASmE,MACdC,IAAY,EACZC,EAAU,EACVC,eAAuB,EACvBC,UAAoD;IAEpD,MAAMC,eAAe7I,gBAAgByI;IACrC,IAAII,iBAAiB9H,WAAW;QAC9B,MAAM,qBAA2C,CAA3C,IAAIY,MAAM,4BAA4B8G,OAAtC,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,2DAA2D;IAC3D,MAAMnH,eAAe,IAAIrB;IACzB0B,MAAMmH,iBAAiB,CAACxH,cAAckH;IAEtC,MAAMO,OAAOH,WAAWG,IAAI;IAC5B,MAAMC,WAAW;QACf,CAACD,KAAK,EAAE,eAAgB,GAAGvD,IAAW;YACpC,MAAMvE,YAAY/B,iBAAiB+J,QAAQ;YAC3C,IAAIhI,cAAcF,WAAW;gBAC3B,MAAM,qBAEL,CAFK,IAAIY,MACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIN,KAAKuH;YAET,MAAM3F,gBAAgB3D,qBAAqB2J,QAAQ;YAEnD,0EAA0E;YAC1E,sFAAsF;YACtF,MAAM9H,0BAA0BzB;YAEhC,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMwJ,UAAUjI,UAAUiI,OAAO;YAEjC,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,uEAAuE;YACvE,MAAMzG,iBACJQ,iBAAiB9D,kBAAkB8B,WAAWgC;YAEhD,MAAMkG,0BACJlG,CAAAA,iCAAAA,cAAehB,IAAI,MAAK,cACpB/B,8BAA8B+C,iBAC9BlC;YAEN,mEAAmE;YACnE,sEAAsE;YACtE,0EAA0E;YAC1E,sEAAsE;YACtE,oEAAoE;YACpE,wEAAwE;YACxE,yEAAyE;YACzE,SAAS;YACT,IAAI,CAACE,UAAUmI,gBAAgB,IAAIC,gBAAgB7D,OAAO;gBACxD,MAAM,CAAC,EAAE8D,MAAM,EAAEC,YAAY,EAAE,CAAC,GAAG/D;gBACnC,iDAAiD;gBACjDA,OAAO;oBAAC;wBAAE8D;wBAAQC;oBAAa;iBAAE;gBAEjClI,KAAK,CAAA;oBACH,CAAC0H,KAAK,EAAE,OAAO,EACbO,QAAQE,gBAAgB,EAC8B,GACtDZ,WAAWvC,KAAK,CAAC,MAAM;4BACrB;gCACEiD,QAAQE;gCACRD,cACEpJ,0CAA0Cc;4BAC9C;yBACD;gBACL,CAAA,CAAC,CAAC8H,KAAK;YACT;YAEA,IAAIJ,kBAAkB,GAAG;gBACvB,IAAInD,KAAKlC,MAAM,KAAK,GAAG;oBACrB,MAAM,qBAEL,CAFK,IAAIzD,eACR,CAAC,kCAAkC,EAAE4J,KAAKC,SAAS,CAACrI,GAAG0H,IAAI,EAAE,gEAAgE,CAAC,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMY,qBAAqBnE,KAAKoE,KAAK;gBACrC,MAAMC,YAAY,MAAMjK,uBAAuB8I,IAAIiB;gBAEnD,IAAI,CAACG,MAAMC,OAAO,CAACF,YAAY;oBAC7B,MAAM,qBAEL,CAFK,IAAIhK,eACR,CAAC,qDAAqD,EAAE4J,KAAKC,SAAS,CAACrI,GAAG0H,IAAI,EAAE,mCAAmC,EAAE,OAAOc,UAAU,SAAS,CAAC,GAD5I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIlB,oBAAoBkB,UAAUvG,MAAM,EAAE;oBACxC,MAAM,qBAEL,CAFK,IAAIzD,eACR,CAAC,kCAAkC,EAAE4J,KAAKC,SAAS,CAACrI,GAAG0H,IAAI,EAAE,YAAY,EAAEJ,gBAAgB,sBAAsB,EAAEkB,UAAUvG,MAAM,CAAC,SAAS,CAAC,GAD1I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAkC,KAAKwE,OAAO,CAACH;YACf;YAEA,MAAMtE,sBAAsBtG;YAE5B,MAAMgL,gBAA+BxH,iBACjC;gBAACyG;gBAASR;gBAAIlD;gBAAM/C;aAAe,GACnC;gBAACyG;gBAASR;gBAAIlD;aAAK;YAEvB,MAAM0E,uBAA0C,MAAMlL,YACpDiL,eACA;gBAAE1E;gBAAqBqB,QAAQuC;YAAwB;YAGzD,MAAMgB,qBACJ,OAAOD,yBAAyB,WAE5B,+CAA+C;YAC/CA,uBACA,MAAMvC,eAAeuC;YAE3B,IAAIzD,SAAqC1F;YAEzC,kEAAkE;YAClE,MAAMqJ,2BAA2BnH,gBAC7B5D,4BAA4B4D,iBAC5B;YACJ,MAAMoH,wBAAwBpH,gBAC1B7D,yBAAyB6D,iBACzB;YAEJ,IAAIoH,uBAAuB;gBACzB,MAAMjF,cACJnC,iBAAiBA,cAAchB,IAAI,KAAK,cACpCgB,cAAcmC,WAAW,GACzB;gBAEN,IAAIA,aAAa;oBACfA,YAAYkF,SAAS;gBACvB;gBACA,MAAMC,cAAcF,sBAAsB7B,KAAK,CAACgC,GAAG,CAACL;gBACpD,IAAII,gBAAgBxJ,WAAW;oBAC7B,MAAM0J,gBAAgB,MAAMF;oBAC5BvH,0BAA0BC,eAAewH;oBACzC,IACExH,kBAAkBlC,aAClBkC,cAAchB,IAAI,KAAK,eACvBwI,kBAAkB1J,aACjB0J,CAAAA,cAAc5I,UAAU,KAAK,KAC5B4I,cAAc3I,MAAM,GAAG/B,cAAa,GACtC;wBACA,6EAA6E;wBAC7E,4EAA4E;wBAC5E,gFAAgF;wBAChF,gFAAgF;wBAChF,IAAIqF,aAAa;4BACfA,YAAYC,OAAO;wBACrB;wBACA,OAAO5F,mBACLwD,cAAc4C,YAAY,EAC1B;oBAEJ;oBACA,MAAM,CAACuB,SAASC,QAAQ,GAAGoD,cAAcpG,KAAK,CAAC4C,GAAG;oBAClDwD,cAAcpG,KAAK,GAAGgD;oBAEtB,IAAIjC,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChCqB,SAAS8B,4BAA4BnB,SAAShC;oBAChD,OAAO;wBACLqB,SAASW;oBACX;gBACF,OAAO;oBACL,IAAIhC,aAAa;wBACfA,YAAYC,OAAO;oBACrB;gBACF;YACF;YAEA,IAAIoB,WAAW1F,WAAW;oBA0BhBkC;gBAzBR,MAAMmC,cACJnC,iBAAiBA,cAAchB,IAAI,KAAK,cACpCgB,cAAcmC,WAAW,GACzB;gBACN,IAAIA,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYkF,SAAS;gBACvB;gBAEA,MAAMI,kBAAkBzJ,UAAU0J,sBAAsB,CAACH,GAAG,CAAC/B;gBAE7D,IAAIiC,mBAAmB,CAACpK,qBAAqBoK,kBAAkB;oBAC7D,MAAMA;gBACR;gBAEA,IAAIxH,QAAQL,sBAAsB5B,WAAWgC,iBACzClC,YACA,mBAAmB8H,eACjB,MAAMA,aAAa2B,GAAG,CAACL,sBAEvB,gEAAgE;gBAChE,8BAA8B;gBAC9B,MAAMtB,aAAa2B,GAAG,CACpBL,oBACAlH,CAAAA,kCAAAA,8BAAAA,cAAeb,YAAY,qBAA3Ba,4BAA6BT,IAAI,KAAI,EAAE;gBAG/C,IAAIU,OAAO;wBACYD;oBAArB,MAAMb,eAAea,CAAAA,kCAAAA,+BAAAA,cAAeb,YAAY,qBAA3Ba,6BAA6BT,IAAI,KAAI,EAAE;oBAC5D,IAAIoI,yBAAyB;oBAE7B,IAAI3H,iCAAAA,cAAeb,YAAY,EAAE;wBAC/B,MAAMyI,iBACJ5H,cAAcb,YAAY,CAAC0I,sBAAsB,CAACN,GAAG,CAAC/B;wBAExD,IAAIoC,gBAAgB;4BAClB,IAAIvK,qBAAqBuK,iBAAiB;gCACxCD,yBAAyBC,eAAexG,KAAK;4BAC/C,OAAO;gCACLuG,yBAAyB,MAAMC;4BACjC;wBACF;oBACF;oBAEA,IACEE,wBACE7H,OACAjC,WACAmB,cACAwI,yBAEF;wBACAjK,yBAAAA,MAAQ,0BAA0BwJ;wBAClCjH,QAAQnC;oBACV;gBACF;gBAEA,MAAMiK,cAAc/E,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACElD,kBAAkBlC,aAClBkC,cAAchB,IAAI,KAAK,eACvBiB,UAAUnC,aACTmC,CAAAA,MAAMrB,UAAU,KAAK,KAAKqB,MAAMpB,MAAM,GAAG/B,cAAa,GACvD;oBACA,6EAA6E;oBAC7E,4EAA4E;oBAC5E,gFAAgF;oBAChF,gFAAgF;oBAChF,IAAIqF,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,OAAO5F,mBACLwD,cAAc4C,YAAY,EAC1B;gBAEJ,OAAO,IACL3C,UAAUnC,aACViK,cAAc9H,MAAMiC,SAAS,GAAGjC,MAAMpB,MAAM,GAAG,QAC9Cb,UAAUgK,kBAAkB,IAC3BD,cAAc9H,MAAMiC,SAAS,GAAGjC,MAAMrB,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,IAAIqB,OAAO;wBACT,IAAI8H,cAAc9H,MAAMiC,SAAS,GAAGjC,MAAMpB,MAAM,GAAG,MAAM;4BACvDnB,yBAAAA,MAAQ,oBAAoBwJ;wBAC9B;wBAEA,IACElJ,UAAUgK,kBAAkB,IAC5BD,cAAc9H,MAAMiC,SAAS,GAAGjC,MAAMrB,UAAU,GAAG,MACnD;4BACAlB,yBAAAA,MAAQ,qCAAqCwJ;wBAC/C;oBACF;oBAEA,MAAM,CAACe,WAAW1D,kBAAkB,GAAG,MAAMxG,mBAC3CC,WACAgC,eACA9B,yBACA+I,sBACA7I,IACAC;oBAGF,gEAAgE;oBAChE,IAAI,CAACL,UAAUkK,WAAW,EAAE;wBAC1B,IAAIC;wBAEJ,IAAIhB,0BAA0B;4BAC5B,8DAA8D;4BAC9D,MAAM1C,QAAQH,uBAAuBC;4BACrC4D,kBAAkB3D,iBAAiBC,OAAO;4BAC1C0C,yBAAyB5B,KAAK,CAAC6C,GAAG,CAChClB,oBACA1C,iBAAiBC,OAAO;wBAE5B,OAAO;4BACL0D,kBAAkB5D;wBACpB;wBAEA,MAAM8D,UAAUzC,aAAawC,GAAG,CAC9BlB,oBACAiB;wBAGFnK,UAAUsK,uBAAuB,KAAK,EAAE;wBACxCtK,UAAUsK,uBAAuB,CAAC9H,IAAI,CAAC6H;oBACzC;oBAEA7E,SAASyE;gBACX,OAAO;oBACLlI,0BAA0BC,eAAeC;oBAEzC,qDAAqD;oBACrDuD,SAASvD,MAAMmB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAI+F,0BAA0B;wBAC5B,MAAM,CAACoB,WAAWC,WAAW,GAAGtE,gBAAgBjE;wBAChD,IAAIkC,aAAa;4BACfqB,SAAS8B,4BAA4BiD,UAAUnH,KAAK,EAAEe;wBACxD,OAAO;4BACLqB,SAAS+E,UAAUnH,KAAK;wBAC1B;wBAEA+F,yBAAyB5B,KAAK,CAAC6C,GAAG,CAChClB,oBACAxE,QAAQC,OAAO,CAAC6F;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClCrG,+BAAAA,YAAaC,OAAO;oBACtB;oBAEA,IAAI2F,cAAc9H,MAAMiC,SAAS,GAAGjC,MAAMrB,UAAU,GAAG,MAAM;wBAC3D,gFAAgF;wBAChF,mEAAmE;wBACnE,MAAM,CAAC6J,eAAelE,kBAAkB,GAAG,MAAMxG,mBAC/CC,WACAF,WACAI,yBACA+I,sBACA7I,IACAC;wBAGF,IAAI8J;wBACJ,IAAIhB,0BAA0B;4BAC5B,MAAM1C,QAAQH,uBAAuBC;4BACrC4D,kBAAkB3D,iBAAiBC,OAAO;4BAC1C0C,yBAAyB5B,KAAK,CAAC6C,GAAG,CAChClB,oBACA1C,iBAAiBC,OAAO;wBAE5B,OAAO;4BACL0D,kBAAkB5D;wBACpB;wBAEA,MAAM8D,UAAUzC,aAAawC,GAAG,CAC9BlB,oBACAiB;wBAGF,IAAI,CAACnK,UAAUsK,uBAAuB,EAAE;4BACtCtK,UAAUsK,uBAAuB,GAAG,EAAE;wBACxC;wBACAtK,UAAUsK,uBAAuB,CAAC9H,IAAI,CAAC6H;wBAEvC,MAAMI,cAAcC,MAAM;oBAC5B;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAWxL,gBACPY,wBAAwB6K,oBAAoB,GAC5C7K,wBAAwB8K,gBAAgB;gBAC5CC,iBAAiBvM;YACnB;YAEA,OAAOZ,yBAAyB0H,QAAQ;gBACtCoF;gBACAtG;gBACAqG;gBACAjF,iBAAiB;YACnB;QACF;IACF,CAAC,CAACoC,KAAK;IAEP,OAAO3I,MAAMoI,KAAK,CAACQ;AACrB;AAEA,SAASK,gBACP7D,IAAW;IAEX,IAAIA,KAAKlC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAAC6I,OAAOC,IAAI,GAAG5G;IAErB,OACE4G,QAAQrL,aAAa,iDAAiD;IACtEoL,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAqCE,iBAAiB;AAE3D;AAEA,SAASxJ,sBACP5B,SAAoB,EACpBgC,aAAwC;IAExC,IAAIhC,UAAUqL,oBAAoB,IAAIrL,UAAUkK,WAAW,EAAE;QAC3D,OAAO;IACT;IAEA,IAAIlK,UAAUsL,GAAG,IAAItJ,eAAe;QAClC,IAAIA,cAAchB,IAAI,KAAK,WAAW;YACpC,OAAOgB,cAAcuJ,OAAO,CAAChC,GAAG,CAAC,qBAAqB;QACxD;QAEA,IAAIvH,cAAchB,IAAI,KAAK,SAAS;YAClC,OAAOgB,cAAcL,eAAe;QACtC;IACF;IAEA,OAAO;AACT;AAEA,SAASmI,wBACP7H,KAAiB,EACjBjC,SAAoB,EACpBmB,YAAsB,EACtBwI,sBAA8B;IAE9B,4EAA4E;IAC5E,6CAA6C;IAC7C,IAAI1H,MAAMV,IAAI,CAACiK,IAAI,CAAC,CAAClJ,MAAQmJ,yBAAyBnJ,KAAKtC,aAAa;QACtE,OAAO;IACT;IAEA,sEAAsE;IACtE,gDAAgD;IAChD,IAAIiC,MAAMiC,SAAS,IAAIyF,wBAAwB;QAC7CjK,yBAAAA,MACE,wBACAuC,MAAMiC,SAAS,EACf,4CACAyF;QAGF,OAAO;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IACxC,IAAIxI,aAAaqK,IAAI,CAAC,CAAClJ,MAAQmJ,yBAAyBnJ,KAAKtC,aAAa;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASyL,yBAAyBnJ,GAAW,EAAEtC,SAAoB;IACjE,MAAM,EAAE0L,yBAAyB,EAAEC,sBAAsB,EAAE,GAAG3L;IAE9D,4EAA4E;IAC5E,IAAI0L,0BAA0BnJ,QAAQ,CAACD,MAAM;QAC3C5C,yBAAAA,MAAQ,OAAO4C,KAAK;QAEpB,OAAO;IACT;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,sEAAsE;IACtE,IAAIqJ,0CAAAA,uBAAwBpJ,QAAQ,CAACD,MAAM;QACzC5C,yBAAAA,MAAQ,OAAO4C,KAAK;QAEpB,OAAO;IACT;IAEA,OAAO;AACT"}