{"version": 3, "sources": ["../../src/server/post-process.ts"], "sourcesContent": ["import type { RenderOpts } from './render'\n\nimport { nonNullable } from '../lib/non-nullable'\n\ntype PostProcessorFunction =\n  | ((html: string) => Promise<string>)\n  | ((html: string) => string)\n\nasync function postProcessHTML(\n  pathname: string,\n  content: string,\n  renderOpts: Pick<\n    RenderOpts,\n    | 'ampOptimizerConfig'\n    | 'ampValidator'\n    | 'ampSkipValidation'\n    | 'optimizeCss'\n    | 'distDir'\n    | 'assetPrefix'\n  >,\n  { inAmpMode, hybridAmp }: { inAmpMode: boolean; hybridAmp: boolean }\n) {\n  const postProcessors: Array<PostProcessorFunction> = [\n    process.env.NEXT_RUNTIME !== 'edge' && inAmpMode && !process.env.TURBOPACK\n      ? async (html: string) => {\n          const optimizeAmp = require('./optimize-amp')\n            .default as typeof import('./optimize-amp').default\n          html = await optimizeAmp!(html, renderOpts.ampOptimizerConfig)\n          if (!renderOpts.ampSkipValidation && renderOpts.ampValidator) {\n            await renderOpts.ampValidator(html, pathname)\n          }\n          return html\n        }\n      : null,\n    process.env.NEXT_RUNTIME !== 'edge' && renderOpts.optimizeCss\n      ? async (html: string) => {\n          // eslint-disable-next-line import/no-extraneous-dependencies\n          const Critters = require('critters')\n          const cssOptimizer = new Critters({\n            ssrMode: true,\n            reduceInlineStyles: false,\n            path: renderOpts.distDir,\n            publicPath: `${renderOpts.assetPrefix}/_next/`,\n            preload: 'media',\n            fonts: false,\n            logLevel:\n              process.env.CRITTERS_LOG_LEVEL ||\n              (process.env.NODE_ENV === 'production' ? 'warn' : 'info'),\n            ...renderOpts.optimizeCss,\n          })\n          return await cssOptimizer.process(html)\n        }\n      : null,\n    inAmpMode || hybridAmp\n      ? (html: string) => {\n          return html.replace(/&amp;amp=1/g, '&amp=1')\n        }\n      : null,\n  ].filter(nonNullable)\n\n  for (const postProcessor of postProcessors) {\n    if (postProcessor) {\n      content = await postProcessor(content)\n    }\n  }\n  return content\n}\n\nexport { postProcessHTML }\n"], "names": ["nonNullable", "postProcessHTML", "pathname", "content", "renderOpts", "inAmpMode", "hybridAmp", "postProcessors", "process", "env", "NEXT_RUNTIME", "TURBOPACK", "html", "optimizeAmp", "require", "default", "ampOptimizerConfig", "ampSkipValidation", "ampValidator", "optimizeCss", "Critters", "cssOptimizer", "ssrMode", "reduceInlineStyles", "path", "distDir", "publicPath", "assetPrefix", "preload", "fonts", "logLevel", "CRITTERS_LOG_LEVEL", "NODE_ENV", "replace", "filter", "postProcessor"], "mappings": "AAEA,SAASA,WAAW,QAAQ,sBAAqB;AAMjD,eAAeC,gBACbC,QAAgB,EAChBC,OAAe,EACfC,UAQC,EACD,EAAEC,SAAS,EAAEC,SAAS,EAA8C;IAEpE,MAAMC,iBAA+C;QACnDC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,aAAa,CAACG,QAAQC,GAAG,CAACE,SAAS,GACtE,OAAOC;YACL,MAAMC,cAAcC,QAAQ,kBACzBC,OAAO;YACVH,OAAO,MAAMC,YAAaD,MAAMR,WAAWY,kBAAkB;YAC7D,IAAI,CAACZ,WAAWa,iBAAiB,IAAIb,WAAWc,YAAY,EAAE;gBAC5D,MAAMd,WAAWc,YAAY,CAACN,MAAMV;YACtC;YACA,OAAOU;QACT,IACA;QACJJ,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUN,WAAWe,WAAW,GACzD,OAAOP;YACL,6DAA6D;YAC7D,MAAMQ,WAAWN,QAAQ;YACzB,MAAMO,eAAe,IAAID,SAAS;gBAChCE,SAAS;gBACTC,oBAAoB;gBACpBC,MAAMpB,WAAWqB,OAAO;gBACxBC,YAAY,GAAGtB,WAAWuB,WAAW,CAAC,OAAO,CAAC;gBAC9CC,SAAS;gBACTC,OAAO;gBACPC,UACEtB,QAAQC,GAAG,CAACsB,kBAAkB,IAC7BvB,CAAAA,QAAQC,GAAG,CAACuB,QAAQ,KAAK,eAAe,SAAS,MAAK;gBACzD,GAAG5B,WAAWe,WAAW;YAC3B;YACA,OAAO,MAAME,aAAab,OAAO,CAACI;QACpC,IACA;QACJP,aAAaC,YACT,CAACM;YACC,OAAOA,KAAKqB,OAAO,CAAC,eAAe;QACrC,IACA;KACL,CAACC,MAAM,CAAClC;IAET,KAAK,MAAMmC,iBAAiB5B,eAAgB;QAC1C,IAAI4B,eAAe;YACjBhC,UAAU,MAAMgC,cAAchC;QAChC;IACF;IACA,OAAOA;AACT;AAEA,SAASF,eAAe,GAAE"}