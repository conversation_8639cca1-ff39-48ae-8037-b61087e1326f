{"version": 3, "sources": ["../../../../../src/server/route-modules/app-route/helpers/is-static-gen-enabled.ts"], "sourcesContent": ["import type { AppRouteModule } from '../module.compiled'\n\n// route handlers are only statically optimized if they define\n// one of these top-level configs manually\n//   - dynamic = 'force-static'\n//   - dynamic = 'error'\n//   - revalidate > 0\n//   - revalidate = false\n//   - generateStaticParams\nexport function isStaticGenEnabled(\n  mod: AppRouteModule['routeModule']['userland']\n) {\n  return (\n    mod.dynamic === 'force-static' ||\n    mod.dynamic === 'error' ||\n    mod.revalidate === false ||\n    (mod.revalidate !== undefined && mod.revalidate > 0) ||\n    typeof mod.generateStaticParams == 'function'\n  )\n}\n"], "names": ["isStaticGenEnabled", "mod", "dynamic", "revalidate", "undefined", "generateStaticParams"], "mappings": "AAEA,8DAA8D;AAC9D,0CAA0C;AAC1C,+BAA+B;AAC/B,wBAAwB;AACxB,qBAAqB;AACrB,yBAAyB;AACzB,2BAA2B;AAC3B,OAAO,SAASA,mBACdC,GAA8C;IAE9C,OACEA,IAAIC,OAAO,KAAK,kBAChBD,IAAIC,OAAO,KAAK,WAChBD,IAAIE,UAAU,KAAK,SAClBF,IAAIE,UAAU,KAAKC,aAAaH,IAAIE,UAAU,GAAG,KAClD,OAAOF,IAAII,oBAAoB,IAAI;AAEvC"}