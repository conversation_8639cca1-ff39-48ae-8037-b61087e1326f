{"version": 3, "sources": ["../../src/lib/generate-interception-routes-rewrites.ts"], "sourcesContent": ["import { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { NEXT_URL } from '../client/components/app-router-headers'\nimport {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../shared/lib/router/utils/interception-routes'\nimport type { Rewrite } from './load-custom-routes'\n\n// a function that converts normalised paths (e.g. /foo/[bar]/[baz]) to the format expected by pathToRegexp (e.g. /foo/:bar/:baz)\nfunction toPathToRegexpPath(path: string): string {\n  return path.replace(/\\[\\[?([^\\]]+)\\]\\]?/g, (_, capture) => {\n    // path-to-regexp only supports word characters, so we replace any non-word characters with underscores\n    const paramName = capture.replace(/\\W+/g, '_')\n\n    // handle catch-all segments (e.g. /foo/bar/[...baz] or /foo/bar/[[...baz]])\n    if (capture.startsWith('...')) {\n      return `:${capture.slice(3)}*`\n    }\n    return ':' + paramName\n  })\n}\n\nexport function generateInterceptionRoutesRewrites(\n  appPaths: string[],\n  basePath = ''\n): Rewrite[] {\n  const rewrites: Rewrite[] = []\n\n  for (const appPath of appPaths) {\n    if (isInterceptionRouteAppPath(appPath)) {\n      const { interceptingRoute, interceptedRoute } =\n        extractInterceptionRouteInformation(appPath)\n\n      const normalizedInterceptingRoute = `${\n        interceptingRoute !== '/' ? toPathToRegexpPath(interceptingRoute) : ''\n      }/(.*)?`\n\n      const normalizedInterceptedRoute = toPathToRegexpPath(interceptedRoute)\n      const normalizedAppPath = toPathToRegexpPath(appPath)\n\n      // pathToRegexp returns a regex that matches the path, but we need to\n      // convert it to a string that can be used in a header value\n      // to the format that Next/the proxy expects\n      let interceptingRouteRegex = pathToRegexp(normalizedInterceptingRoute)\n        .toString()\n        .slice(2, -3)\n\n      rewrites.push({\n        source: `${basePath}${normalizedInterceptedRoute}`,\n        destination: `${basePath}${normalizedAppPath}`,\n        has: [\n          {\n            type: 'header',\n            key: NEXT_URL,\n            value: interceptingRouteRegex,\n          },\n        ],\n      })\n    }\n  }\n\n  return rewrites\n}\n\nexport function isInterceptionRouteRewrite(route: Rewrite) {\n  // When we generate interception rewrites in the above implementation, we always do so with only a single `has` condition.\n  return route.has?.[0]?.key === NEXT_URL\n}\n"], "names": ["generateInterceptionRoutesRewrites", "isInterceptionRouteRewrite", "toPathToRegexpPath", "path", "replace", "_", "capture", "paramName", "startsWith", "slice", "appPaths", "basePath", "rewrites", "appPath", "isInterceptionRouteAppPath", "interceptingRoute", "interceptedRoute", "extractInterceptionRouteInformation", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "pathToRegexp", "toString", "push", "source", "destination", "has", "type", "key", "NEXT_URL", "value", "route"], "mappings": ";;;;;;;;;;;;;;;IAsBgBA,kCAAkC;eAAlCA;;IA0CAC,0BAA0B;eAA1BA;;;8BAhEa;kCACJ;oCAIlB;AAGP,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,uGAAuG;QACvG,MAAMC,YAAYD,QAAQF,OAAO,CAAC,QAAQ;QAE1C,4EAA4E;QAC5E,IAAIE,QAAQE,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAEF,QAAQG,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEO,SAASP,mCACdU,QAAkB,EAClBC,WAAW,EAAE;IAEb,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWH,SAAU;QAC9B,IAAII,IAAAA,8CAA0B,EAACD,UAAU;YACvC,MAAM,EAAEE,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CC,IAAAA,uDAAmC,EAACJ;YAEtC,MAAMK,8BAA8B,GAClCH,sBAAsB,MAAMb,mBAAmBa,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMI,6BAA6BjB,mBAAmBc;YACtD,MAAMI,oBAAoBlB,mBAAmBW;YAE7C,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIQ,yBAAyBC,IAAAA,0BAAY,EAACJ,6BACvCK,QAAQ,GACRd,KAAK,CAAC,GAAG,CAAC;YAEbG,SAASY,IAAI,CAAC;gBACZC,QAAQ,GAAGd,WAAWQ,4BAA4B;gBAClDO,aAAa,GAAGf,WAAWS,mBAAmB;gBAC9CO,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKC,0BAAQ;wBACbC,OAAOV;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOT;AACT;AAEO,SAASX,2BAA2B+B,KAAc;QAEhDA,aAAAA;IADP,0HAA0H;IAC1H,OAAOA,EAAAA,aAAAA,MAAML,GAAG,sBAATK,cAAAA,UAAW,CAAC,EAAE,qBAAdA,YAAgBH,GAAG,MAAKC,0BAAQ;AACzC"}