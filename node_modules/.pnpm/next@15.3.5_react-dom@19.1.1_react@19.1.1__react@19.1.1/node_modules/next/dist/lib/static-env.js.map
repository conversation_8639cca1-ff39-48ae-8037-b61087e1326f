{"version": 3, "sources": ["../../src/lib/static-env.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\n\nfunction errorIfEnvConflicted(config: NextConfigComplete, key: string) {\n  const isPrivateKey = /^(?:NODE_.+)|^(?:__.+)$/i.test(key)\n  const hasNextRuntimeKey = key === 'NEXT_RUNTIME'\n\n  if (isPrivateKey || hasNextRuntimeKey) {\n    throw new Error(\n      `The key \"${key}\" under \"env\" in ${config.configFileName} is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n    )\n  }\n}\n\n/**\n * Collects all environment variables that are using the `NEXT_PUBLIC_` prefix.\n */\nexport function getNextPublicEnvironmentVariables() {\n  const defineEnv: Record<string, string | undefined> = {}\n  for (const key in process.env) {\n    if (key.startsWith('NEXT_PUBLIC_')) {\n      const value = process.env[key]\n      if (value != null) {\n        defineEnv[`process.env.${key}`] = value\n      }\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Collects the `env` config value from the Next.js config.\n */\nexport function getNextConfigEnv(config: NextConfigComplete) {\n  // Refactored code below to use for-of\n  const defineEnv: Record<string, string | undefined> = {}\n  const env = config.env\n  for (const key in env) {\n    const value = env[key]\n    if (value != null) {\n      errorIfEnvConflicted(config, key)\n      defineEnv[`process.env.${key}`] = value\n    }\n  }\n  return defineEnv\n}\n\nexport function getStaticEnv(config: NextConfigComplete) {\n  const staticEnv: Record<string, string | undefined> = {\n    ...getNextPublicEnvironmentVariables(),\n    ...getNextConfigEnv(config),\n    'process.env.NEXT_DEPLOYMENT_ID': config.deploymentId || '',\n  }\n  return staticEnv\n}\n\nexport function populateStaticEnv(config: NextConfigComplete) {\n  // since inlining comes after static generation we need\n  // to ensure this value is assigned to process env so it\n  // can still be accessed\n  const staticEnv = getStaticEnv(config)\n  for (const key in staticEnv) {\n    const innerKey = key.split('.').pop() || ''\n    if (!process.env[innerKey]) {\n      process.env[innerKey] = staticEnv[key] || ''\n    }\n  }\n}\n"], "names": ["getNextConfigEnv", "getNextPublicEnvironmentVariables", "getStaticEnv", "populateStaticEnv", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "defineEnv", "process", "env", "startsWith", "value", "staticEnv", "deploymentId", "innerKey", "split", "pop"], "mappings": ";;;;;;;;;;;;;;;;;IAgCgBA,gBAAgB;eAAhBA;;IAhBAC,iCAAiC;eAAjCA;;IA8BAC,YAAY;eAAZA;;IASAC,iBAAiB;eAAjBA;;;AArDhB,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC,GAD3H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAKO,SAASV;IACd,MAAMW,YAAgD,CAAC;IACvD,IAAK,MAAMN,OAAOO,QAAQC,GAAG,CAAE;QAC7B,IAAIR,IAAIS,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACR,IAAI;YAC9B,IAAIU,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEN,KAAK,CAAC,GAAGU;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASZ,iBAAiBK,MAA0B;IACzD,sCAAsC;IACtC,MAAMO,YAAgD,CAAC;IACvD,MAAME,MAAMT,OAAOS,GAAG;IACtB,IAAK,MAAMR,OAAOQ,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACR,IAAI;QACtB,IAAIU,SAAS,MAAM;YACjBZ,qBAAqBC,QAAQC;YAC7BM,SAAS,CAAC,CAAC,YAAY,EAAEN,KAAK,CAAC,GAAGU;QACpC;IACF;IACA,OAAOJ;AACT;AAEO,SAASV,aAAaG,MAA0B;IACrD,MAAMY,YAAgD;QACpD,GAAGhB,mCAAmC;QACtC,GAAGD,iBAAiBK,OAAO;QAC3B,kCAAkCA,OAAOa,YAAY,IAAI;IAC3D;IACA,OAAOD;AACT;AAEO,SAASd,kBAAkBE,MAA0B;IAC1D,uDAAuD;IACvD,wDAAwD;IACxD,wBAAwB;IACxB,MAAMY,YAAYf,aAAaG;IAC/B,IAAK,MAAMC,OAAOW,UAAW;QAC3B,MAAME,WAAWb,IAAIc,KAAK,CAAC,KAAKC,GAAG,MAAM;QACzC,IAAI,CAACR,QAAQC,GAAG,CAACK,SAAS,EAAE;YAC1BN,QAAQC,GAAG,CAACK,SAAS,GAAGF,SAAS,CAACX,IAAI,IAAI;QAC5C;IACF;AACF"}