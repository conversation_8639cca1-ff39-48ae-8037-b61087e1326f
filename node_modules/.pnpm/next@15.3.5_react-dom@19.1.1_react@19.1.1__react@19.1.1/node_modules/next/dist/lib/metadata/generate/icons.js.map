{"version": 3, "sources": ["../../../../src/lib/metadata/generate/icons.tsx"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { Icon, IconDescriptor } from '../types/metadata-types'\n\nimport { MetaFilter } from './meta'\n\nfunction IconDescriptorLink({ icon }: { icon: IconDescriptor }) {\n  const { url, rel = 'icon', ...props } = icon\n\n  return <link rel={rel} href={url.toString()} {...props} />\n}\n\nfunction IconLink({ rel, icon }: { rel?: string; icon: Icon }) {\n  if (typeof icon === 'object' && !(icon instanceof URL)) {\n    if (!icon.rel && rel) icon.rel = rel\n    return IconDescriptorLink({ icon })\n  } else {\n    const href = icon.toString()\n    return <link rel={rel} href={href} />\n  }\n}\n\nexport function IconsMetadata({ icons }: { icons: ResolvedMetadata['icons'] }) {\n  if (!icons) return null\n\n  const shortcutList = icons.shortcut\n  const iconList = icons.icon\n  const appleList = icons.apple\n  const otherList = icons.other\n\n  return MetaFilter([\n    shortcutList\n      ? shortcutList.map((icon) => IconLink({ rel: 'shortcut icon', icon }))\n      : null,\n    iconList ? iconList.map((icon) => IconLink({ rel: 'icon', icon })) : null,\n    appleList\n      ? appleList.map((icon) => IconLink({ rel: 'apple-touch-icon', icon }))\n      : null,\n    otherList ? otherList.map((icon) => IconDescriptorLink({ icon })) : null,\n  ])\n}\n"], "names": ["IconsMetadata", "IconDescriptorLink", "icon", "url", "rel", "props", "link", "href", "toString", "IconLink", "URL", "icons", "shortcutList", "shortcut", "iconList", "appleList", "apple", "otherList", "other", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map"], "mappings": ";;;;+BAqBgBA;;;eAAAA;;;;sBAlBW;AAE3B,SAASC,mBAAmB,EAAEC,IAAI,EAA4B;IAC5D,MAAM,EAAEC,GAAG,EAAEC,MAAM,MAAM,EAAE,GAAGC,OAAO,GAAGH;IAExC,qBAAO,qBAACI;QAAKF,KAAKA;QAAKG,MAAMJ,IAAIK,QAAQ;QAAK,GAAGH,KAAK;;AACxD;AAEA,SAASI,SAAS,EAAEL,GAAG,EAAEF,IAAI,EAAgC;IAC3D,IAAI,OAAOA,SAAS,YAAY,CAAEA,CAAAA,gBAAgBQ,GAAE,GAAI;QACtD,IAAI,CAACR,KAAKE,GAAG,IAAIA,KAAKF,KAAKE,GAAG,GAAGA;QACjC,OAAOH,mBAAmB;YAAEC;QAAK;IACnC,OAAO;QACL,MAAMK,OAAOL,KAAKM,QAAQ;QAC1B,qBAAO,qBAACF;YAAKF,KAAKA;YAAKG,MAAMA;;IAC/B;AACF;AAEO,SAASP,cAAc,EAAEW,KAAK,EAAwC;IAC3E,IAAI,CAACA,OAAO,OAAO;IAEnB,MAAMC,eAAeD,MAAME,QAAQ;IACnC,MAAMC,WAAWH,MAAMT,IAAI;IAC3B,MAAMa,YAAYJ,MAAMK,KAAK;IAC7B,MAAMC,YAAYN,MAAMO,KAAK;IAE7B,OAAOC,IAAAA,gBAAU,EAAC;QAChBP,eACIA,aAAaQ,GAAG,CAAC,CAAClB,OAASO,SAAS;gBAAEL,KAAK;gBAAiBF;YAAK,MACjE;QACJY,WAAWA,SAASM,GAAG,CAAC,CAAClB,OAASO,SAAS;gBAAEL,KAAK;gBAAQF;YAAK,MAAM;QACrEa,YACIA,UAAUK,GAAG,CAAC,CAAClB,OAASO,SAAS;gBAAEL,KAAK;gBAAoBF;YAAK,MACjE;QACJe,YAAYA,UAAUG,GAAG,CAAC,CAAClB,OAASD,mBAAmB;gBAAEC;YAAK,MAAM;KACrE;AACH"}