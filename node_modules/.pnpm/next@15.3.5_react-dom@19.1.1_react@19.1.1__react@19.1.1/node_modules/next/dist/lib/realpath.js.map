{"version": 3, "sources": ["../../src/lib/realpath.ts"], "sourcesContent": ["import fs from 'fs'\n\nconst isWindows = process.platform === 'win32'\n\n// Interesting learning from this, that fs.realpathSync is 70x slower than fs.realpathSync.native:\n// https://sun0day.github.io/blog/vite/why-vite4_3-is-faster.html#fs-realpathsync-issue\n// https://github.com/nodejs/node/issues/2680\n// However, we can't use fs.realpathSync.native on Windows due to behavior differences.\nexport const realpathSync = isWindows ? fs.realpathSync : fs.realpathSync.native\n"], "names": ["realpathSync", "isWindows", "process", "platform", "fs", "native"], "mappings": ";;;;+BAQaA;;;eAAAA;;;2DARE;;;;;;AAEf,MAAMC,YAAYC,QAAQC,QAAQ,KAAK;AAMhC,MAAMH,eAAeC,YAAYG,WAAE,CAACJ,YAAY,GAAGI,WAAE,CAACJ,YAAY,CAACK,MAAM"}