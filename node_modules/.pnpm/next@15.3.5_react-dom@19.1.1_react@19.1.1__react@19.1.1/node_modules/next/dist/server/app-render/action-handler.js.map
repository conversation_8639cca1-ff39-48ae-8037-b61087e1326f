{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'node:http'\nimport type { SizeLimit } from '../../types'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { AppRenderContext, GenerateFlight } from './app-render'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\n\nimport {\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  ACTION_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getRedirectTypeFromError,\n  getURLFromRedirectError,\n} from '../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectType,\n} from '../../client/components/redirect-error'\nimport RenderResult from '../render-result'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  filterReqHeaders,\n  actionsForbiddenHeaders,\n} from '../lib/server-ipc/utils'\nimport { getModifiedCookieValues } from '../web/spec-extension/adapters/request-cookies'\n\nimport {\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n} from '../../lib/constants'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { isCsrfOriginAllowed } from './csrf-protection'\nimport { warn } from '../../build/output/log'\nimport { RequestCookies, ResponseCookies } from '../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport { fromNodeOutgoingHttpHeaders } from '../web/utils'\nimport { selectWorkerForForwarding } from './action-utils'\nimport { isNodeNextRequest, isWebNextRequest } from '../base-http/helpers'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { synchronizeMutableCookies } from '../async-storage/request-store'\nimport type { TemporaryReferenceSet } from 'react-server-dom-webpack/server.edge'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { executeRevalidates } from '../revalidation-utils'\n\nfunction formDataFromSearchQueryString(query: string) {\n  const searchParams = new URLSearchParams(query)\n  const formData = new FormData()\n  for (const [key, value] of searchParams) {\n    formData.append(key, value)\n  }\n  return formData\n}\n\nfunction nodeHeadersToRecord(\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders\n) {\n  const record: Record<string, string> = {}\n  for (const [key, value] of Object.entries(headers)) {\n    if (value !== undefined) {\n      record[key] = Array.isArray(value) ? value.join(', ') : `${value}`\n    }\n  }\n  return record\n}\n\nfunction getForwardedHeaders(\n  req: BaseNextRequest,\n  res: BaseNextResponse\n): Headers {\n  // Get request headers and cookies\n  const requestHeaders = req.headers\n  const requestCookies = new RequestCookies(HeadersAdapter.from(requestHeaders))\n\n  // Get response headers and cookies\n  const responseHeaders = res.getHeaders()\n  const responseCookies = new ResponseCookies(\n    fromNodeOutgoingHttpHeaders(responseHeaders)\n  )\n\n  // Merge request and response headers\n  const mergedHeaders = filterReqHeaders(\n    {\n      ...nodeHeadersToRecord(requestHeaders),\n      ...nodeHeadersToRecord(responseHeaders),\n    },\n    actionsForbiddenHeaders\n  ) as Record<string, string>\n\n  // Merge cookies into requestCookies, so responseCookies always take precedence\n  // and overwrite/delete those from requestCookies.\n  responseCookies.getAll().forEach((cookie) => {\n    if (typeof cookie.value === 'undefined') {\n      requestCookies.delete(cookie.name)\n    } else {\n      requestCookies.set(cookie)\n    }\n  })\n\n  // Update the 'cookie' header with the merged cookies\n  mergedHeaders['cookie'] = requestCookies.toString()\n\n  // Remove headers that should not be forwarded\n  delete mergedHeaders['transfer-encoding']\n\n  return new Headers(mergedHeaders)\n}\n\nfunction addRevalidationHeader(\n  res: BaseNextResponse,\n  {\n    workStore,\n    requestStore,\n  }: {\n    workStore: WorkStore\n    requestStore: RequestStore\n  }\n) {\n  // If a tag was revalidated, the client router needs to invalidate all the\n  // client router cache as they may be stale. And if a path was revalidated, the\n  // client needs to invalidate all subtrees below that path.\n\n  // To keep the header size small, we use a tuple of\n  // [[revalidatedPaths], isTagRevalidated ? 1 : 0, isCookieRevalidated ? 1 : 0]\n  // instead of a JSON object.\n\n  // TODO-APP: Currently the prefetch cache doesn't have subtree information,\n  // so we need to invalidate the entire cache if a path was revalidated.\n  // TODO-APP: Currently paths are treated as tags, so the second element of the tuple\n  // is always empty.\n\n  const isTagRevalidated = workStore.pendingRevalidatedTags?.length ? 1 : 0\n  const isCookieRevalidated = getModifiedCookieValues(\n    requestStore.mutableCookies\n  ).length\n    ? 1\n    : 0\n\n  res.setHeader(\n    'x-action-revalidated',\n    JSON.stringify([[], isTagRevalidated, isCookieRevalidated])\n  )\n}\n\n/**\n * Forwards a server action request to a separate worker. Used when the requested action is not available in the current worker.\n */\nasync function createForwardedActionResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  host: Host,\n  workerPathname: string,\n  basePath: string,\n  workStore: WorkStore\n) {\n  if (!host) {\n    throw new Error(\n      'Invariant: Missing `host` header from a forwarded Server Actions request.'\n    )\n  }\n\n  const forwardedHeaders = getForwardedHeaders(req, res)\n\n  // indicate that this action request was forwarded from another worker\n  // we use this to skip rendering the flight tree so that we don't update the UI\n  // with the response from the forwarded worker\n  forwardedHeaders.set('x-action-forwarded', '1')\n\n  const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n  // For standalone or the serverful mode, use the internal origin directly\n  // other than the host headers from the request.\n  const origin = process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${host.value}`\n\n  const fetchUrl = new URL(`${origin}${basePath}${workerPathname}`)\n\n  try {\n    let body: BodyInit | ReadableStream<Uint8Array> | undefined\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(req)\n    ) {\n      if (!req.body) {\n        throw new Error('Invariant: missing request body.')\n      }\n\n      body = req.body\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      body = req.stream()\n    } else {\n      throw new Error('Invariant: Unknown request type.')\n    }\n\n    // Forward the request to the new worker\n    const response = await fetch(fetchUrl, {\n      method: 'POST',\n      body,\n      duplex: 'half',\n      headers: forwardedHeaders,\n      redirect: 'manual',\n      next: {\n        // @ts-ignore\n        internal: 1,\n      },\n    })\n\n    if (\n      response.headers.get('content-type')?.startsWith(RSC_CONTENT_TYPE_HEADER)\n    ) {\n      // copy the headers from the redirect response to the response we're sending\n      for (const [key, value] of response.headers) {\n        if (!actionsForbiddenHeaders.includes(key)) {\n          res.setHeader(key, value)\n        }\n      }\n\n      return new FlightRenderResult(response.body!)\n    } else {\n      // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n      response.body?.cancel()\n    }\n  } catch (err) {\n    // we couldn't stream the forwarded response, so we'll just return an empty response\n    console.error(`failed to forward action response`, err)\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n/**\n * Returns the parsed redirect URL if we deem that it is hosted by us.\n *\n * We handle both relative and absolute redirect URLs.\n *\n * In case the redirect URL is not relative to the application we return `null`.\n */\nfunction getAppRelativeRedirectUrl(\n  basePath: string,\n  host: Host,\n  redirectUrl: string\n): URL | null {\n  if (redirectUrl.startsWith('/') || redirectUrl.startsWith('.')) {\n    // Make sure we are appending the basePath to relative URLS\n    return new URL(`${basePath}${redirectUrl}`, 'http://n')\n  }\n\n  const parsedRedirectUrl = new URL(redirectUrl)\n\n  if (host?.value !== parsedRedirectUrl.host) {\n    return null\n  }\n\n  // At this point the hosts are the same, just confirm we\n  // are routing to a path underneath the `basePath`\n  return parsedRedirectUrl.pathname.startsWith(basePath)\n    ? parsedRedirectUrl\n    : null\n}\n\nasync function createRedirectRenderResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  originalHost: Host,\n  redirectUrl: string,\n  redirectType: RedirectType,\n  basePath: string,\n  workStore: WorkStore\n) {\n  res.setHeader('x-action-redirect', `${redirectUrl};${redirectType}`)\n\n  // If we're redirecting to another route of this Next.js application, we'll\n  // try to stream the response from the other worker path. When that works,\n  // we can save an extra roundtrip and avoid a full page reload.\n  // When the redirect URL starts with a `/` or is to the same host, under the\n  // `basePath` we treat it as an app-relative redirect;\n  const appRelativeRedirectUrl = getAppRelativeRedirectUrl(\n    basePath,\n    originalHost,\n    redirectUrl\n  )\n\n  if (appRelativeRedirectUrl) {\n    if (!originalHost) {\n      throw new Error(\n        'Invariant: Missing `host` header from a forwarded Server Actions request.'\n      )\n    }\n\n    const forwardedHeaders = getForwardedHeaders(req, res)\n    forwardedHeaders.set(RSC_HEADER, '1')\n\n    const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n    // For standalone or the serverful mode, use the internal origin directly\n    // other than the host headers from the request.\n    const origin =\n      process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${originalHost.value}`\n\n    const fetchUrl = new URL(\n      `${origin}${appRelativeRedirectUrl.pathname}${appRelativeRedirectUrl.search}`\n    )\n\n    if (workStore.pendingRevalidatedTags) {\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n        workStore.pendingRevalidatedTags.join(',')\n      )\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n        workStore.incrementalCache?.prerenderManifest?.preview?.previewModeId ||\n          ''\n      )\n    }\n\n    // Ensures that when the path was revalidated we don't return a partial response on redirects\n    forwardedHeaders.delete(NEXT_ROUTER_STATE_TREE_HEADER)\n    // When an action follows a redirect, it's no longer handling an action: it's just a normal RSC request\n    // to the requested URL. We should remove the `next-action` header so that it's not treated as an action\n    forwardedHeaders.delete(ACTION_HEADER)\n\n    try {\n      const response = await fetch(fetchUrl, {\n        method: 'GET',\n        headers: forwardedHeaders,\n        next: {\n          // @ts-ignore\n          internal: 1,\n        },\n      })\n\n      if (\n        response.headers\n          .get('content-type')\n          ?.startsWith(RSC_CONTENT_TYPE_HEADER)\n      ) {\n        // copy the headers from the redirect response to the response we're sending\n        for (const [key, value] of response.headers) {\n          if (!actionsForbiddenHeaders.includes(key)) {\n            res.setHeader(key, value)\n          }\n        }\n\n        return new FlightRenderResult(response.body!)\n      } else {\n        // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n        response.body?.cancel()\n      }\n    } catch (err) {\n      // we couldn't stream the redirect response, so we'll just do a normal redirect\n      console.error(`failed to get redirect response`, err)\n    }\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n// Used to compare Host header and Origin header.\nconst enum HostType {\n  XForwardedHost = 'x-forwarded-host',\n  Host = 'host',\n}\ntype Host =\n  | {\n      type: HostType.XForwardedHost\n      value: string\n    }\n  | {\n      type: HostType.Host\n      value: string\n    }\n  | undefined\n\n/**\n * Ensures the value of the header can't create long logs.\n */\nfunction limitUntrustedHeaderValueForLogs(value: string) {\n  return value.length > 100 ? value.slice(0, 100) + '...' : value\n}\n\nexport function parseHostHeader(\n  headers: IncomingHttpHeaders,\n  originDomain?: string\n) {\n  const forwardedHostHeader = headers['x-forwarded-host']\n  const forwardedHostHeaderValue =\n    forwardedHostHeader && Array.isArray(forwardedHostHeader)\n      ? forwardedHostHeader[0]\n      : forwardedHostHeader?.split(',')?.[0]?.trim()\n  const hostHeader = headers['host']\n\n  if (originDomain) {\n    return forwardedHostHeaderValue === originDomain\n      ? {\n          type: HostType.XForwardedHost,\n          value: forwardedHostHeaderValue,\n        }\n      : hostHeader === originDomain\n        ? {\n            type: HostType.Host,\n            value: hostHeader,\n          }\n        : undefined\n  }\n\n  return forwardedHostHeaderValue\n    ? {\n        type: HostType.XForwardedHost,\n        value: forwardedHostHeaderValue,\n      }\n    : hostHeader\n      ? {\n          type: HostType.Host,\n          value: hostHeader,\n        }\n      : undefined\n}\n\ntype ServerModuleMap = Record<\n  string,\n  {\n    id: string\n    chunks: string[]\n    name: string\n  }\n>\n\ntype ServerActionsConfig = {\n  bodySizeLimit?: SizeLimit\n  allowedOrigins?: string[]\n}\n\nexport async function handleAction({\n  req,\n  res,\n  ComponentMod,\n  serverModuleMap,\n  generateFlight,\n  workStore,\n  requestStore,\n  serverActions,\n  ctx,\n}: {\n  req: BaseNextRequest\n  res: BaseNextResponse\n  ComponentMod: AppPageModule\n  serverModuleMap: ServerModuleMap\n  generateFlight: GenerateFlight\n  workStore: WorkStore\n  requestStore: RequestStore\n  serverActions?: ServerActionsConfig\n  ctx: AppRenderContext\n}): Promise<\n  | undefined\n  | {\n      type: 'not-found'\n    }\n  | {\n      type: 'done'\n      result: RenderResult | undefined\n      formState?: any\n    }\n> {\n  const contentType = req.headers['content-type']\n  const { serverActionsManifest, page } = ctx.renderOpts\n\n  const {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  } = getServerActionRequestMetadata(req)\n\n  // If it can't be a Server Action, skip handling.\n  // Note that this can be a false positive -- any multipart/urlencoded POST can get us here,\n  // But won't know if it's an MPA action or not until we call `decodeAction` below.\n  if (!isPossibleServerAction) {\n    return\n  }\n\n  if (workStore.isStaticGeneration) {\n    throw new Error(\n      \"Invariant: server actions can't be handled during static rendering\"\n    )\n  }\n\n  let temporaryReferences: TemporaryReferenceSet | undefined\n\n  const finalizeAndGenerateFlight: GenerateFlight = (...args) => {\n    // When we switch to the render phase, cookies() will return\n    // `workUnitStore.cookies` instead of `workUnitStore.userspaceMutableCookies`.\n    // We want the render to see any cookie writes that we performed during the action,\n    // so we need to update the immutable cookies to reflect the changes.\n    synchronizeMutableCookies(requestStore)\n\n    // The server action might have toggled draft mode, so we need to reflect\n    // that in the work store to be up-to-date for subsequent rendering.\n    workStore.isDraftMode = requestStore.draftMode.isEnabled\n\n    return generateFlight(...args)\n  }\n\n  // When running actions the default is no-store, you can still `cache: 'force-cache'`\n  workStore.fetchCache = 'default-no-store'\n\n  const originDomain =\n    typeof req.headers['origin'] === 'string'\n      ? new URL(req.headers['origin']).host\n      : undefined\n  const host = parseHostHeader(req.headers)\n\n  let warning: string | undefined = undefined\n\n  function warnBadServerActionRequest() {\n    if (warning) {\n      warn(warning)\n    }\n  }\n  // This is to prevent CSRF attacks. If `x-forwarded-host` is set, we need to\n  // ensure that the request is coming from the same host.\n  if (!originDomain) {\n    // This might be an old browser that doesn't send `host` header. We ignore\n    // this case.\n    warning = 'Missing `origin` header from a forwarded Server Actions request.'\n  } else if (!host || originDomain !== host.value) {\n    // If the customer sets a list of allowed origins, we'll allow the request.\n    // These are considered safe but might be different from forwarded host set\n    // by the infra (i.e. reverse proxies).\n    if (isCsrfOriginAllowed(originDomain, serverActions?.allowedOrigins)) {\n      // Ignore it\n    } else {\n      if (host) {\n        // This seems to be an CSRF attack. We should not proceed the action.\n        console.error(\n          `\\`${\n            host.type\n          }\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            host.value\n          )}\\` does not match \\`origin\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            originDomain\n          )}\\` from a forwarded Server Actions request. Aborting the action.`\n        )\n      } else {\n        // This is an attack. We should not proceed the action.\n        console.error(\n          `\\`x-forwarded-host\\` or \\`host\\` headers are not provided. One of these is needed to compare the \\`origin\\` header from a forwarded Server Actions request. Aborting the action.`\n        )\n      }\n\n      const error = new Error('Invalid Server Actions request.')\n\n      if (isFetchAction) {\n        res.statusCode = 500\n        await executeRevalidates(workStore)\n\n        const promise = Promise.reject(error)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            actionResult: promise,\n            // if the page was not revalidated, we can skip the rendering the flight tree\n            skipFlight: !workStore.pathWasRevalidated,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      throw error\n    }\n  }\n\n  // ensure we avoid caching server actions unexpectedly\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  let boundActionArguments: unknown[] = []\n\n  const { actionAsyncStorage } = ComponentMod\n\n  let actionResult: RenderResult | undefined\n  let formState: any | undefined\n  let actionModId: string | undefined\n  const actionWasForwarded = Boolean(req.headers['x-action-forwarded'])\n\n  if (actionId) {\n    const forwardedWorker = selectWorkerForForwarding(\n      actionId,\n      page,\n      serverActionsManifest\n    )\n\n    // If forwardedWorker is truthy, it means there isn't a worker for the action\n    // in the current handler, so we forward the request to a worker that has the action.\n    if (forwardedWorker) {\n      return {\n        type: 'done',\n        result: await createForwardedActionResponse(\n          req,\n          res,\n          host,\n          forwardedWorker,\n          ctx.renderOpts.basePath,\n          workStore\n        ),\n      }\n    }\n  }\n\n  try {\n    await actionAsyncStorage.run({ isAction: true }, async () => {\n      if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME === 'edge' &&\n        isWebNextRequest(req)\n      ) {\n        if (!req.body) {\n          throw new Error('invariant: Missing request body.')\n        }\n\n        // TODO: add body limit\n\n        // Use react-server-dom-webpack/server.edge\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeAction,\n          decodeFormState,\n        } = ComponentMod\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        if (isMultipartAction) {\n          // TODO-APP: Add streaming support\n          const formData = await req.request.formData()\n          if (isFetchAction) {\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              let actionReturnedState: unknown\n              requestStore.phase = 'action'\n              try {\n                actionReturnedState = await workUnitAsyncStorage.run(\n                  requestStore,\n                  action\n                )\n              } finally {\n                requestStore.phase = 'render'\n              }\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          const reader = req.body.getReader()\n          while (true) {\n            const { done, value } = await reader.read()\n            if (done) {\n              break\n            }\n\n            chunks.push(value)\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        isNodeNextRequest(req)\n      ) {\n        // Use react-server-dom-webpack/server.node which supports streaming\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeReplyFromBusboy,\n          decodeAction,\n          decodeFormState,\n        } = require(\n          `./react-server.node`\n        ) as typeof import('./react-server.node')\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        const { Transform } =\n          require('node:stream') as typeof import('node:stream')\n\n        const defaultBodySizeLimit = '1 MB'\n        const bodySizeLimit =\n          serverActions?.bodySizeLimit ?? defaultBodySizeLimit\n        const bodySizeLimitBytes =\n          bodySizeLimit !== defaultBodySizeLimit\n            ? (\n                require('next/dist/compiled/bytes') as typeof import('bytes')\n              ).parse(bodySizeLimit)\n            : 1024 * 1024 // 1 MB\n\n        let size = 0\n        const body = req.body.pipe(\n          new Transform({\n            transform(chunk, encoding, callback) {\n              size += Buffer.byteLength(chunk, encoding)\n              if (size > bodySizeLimitBytes) {\n                const { ApiError } = require('../api-utils')\n\n                callback(\n                  new ApiError(\n                    413,\n                    `Body exceeded ${bodySizeLimit} limit.\n                To configure the body size limit for Server Actions, see: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit`\n                  )\n                )\n                return\n              }\n\n              callback(null, chunk)\n            },\n          })\n        )\n\n        if (isMultipartAction) {\n          if (isFetchAction) {\n            const busboy = (require('busboy') as typeof import('busboy'))({\n              defParamCharset: 'utf8',\n              headers: req.headers,\n              limits: { fieldSize: bodySizeLimitBytes },\n            })\n\n            body.pipe(busboy)\n\n            boundActionArguments = await decodeReplyFromBusboy(\n              busboy,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            // React doesn't yet publish a busboy version of decodeAction\n            // so we polyfill the parsing of FormData.\n            const fakeRequest = new Request('http://localhost', {\n              method: 'POST',\n              // @ts-expect-error\n              headers: { 'Content-Type': contentType },\n              body: new ReadableStream({\n                start: (controller) => {\n                  body.on('data', (chunk) => {\n                    controller.enqueue(new Uint8Array(chunk))\n                  })\n                  body.on('end', () => {\n                    controller.close()\n                  })\n                  body.on('error', (err) => {\n                    controller.error(err)\n                  })\n                },\n              }),\n              duplex: 'half',\n            })\n            const formData = await fakeRequest.formData()\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              let actionReturnedState: unknown\n              requestStore.phase = 'action'\n              try {\n                actionReturnedState = await workUnitAsyncStorage.run(\n                  requestStore,\n                  action\n                )\n              } finally {\n                requestStore.phase = 'render'\n              }\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          for await (const chunk of req.body) {\n            chunks.push(Buffer.from(chunk))\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else {\n        throw new Error('Invariant: Unknown request type.')\n      }\n\n      // actions.js\n      // app/page.js\n      //   action worker1\n      //     appRender1\n\n      // app/foo/page.js\n      //   action worker2\n      //     appRender\n\n      // / -> fire action -> POST / -> appRender1 -> modId for the action file\n      // /foo -> fire action -> POST /foo -> appRender2 -> modId for the action file\n\n      try {\n        actionModId =\n          actionModId ?? getActionModIdOrError(actionId, serverModuleMap)\n      } catch (err) {\n        if (actionId !== null) {\n          console.error(err)\n        }\n        return {\n          type: 'not-found',\n        }\n      }\n\n      const actionMod = (await ComponentMod.__next_app__.require(\n        actionModId\n      )) as Record<string, (...args: unknown[]) => Promise<unknown>>\n      const actionHandler =\n        actionMod[\n          // `actionId` must exist if we got here, as otherwise we would have thrown an error above\n          actionId!\n        ]\n\n      let returnVal: unknown\n      requestStore.phase = 'action'\n      try {\n        returnVal = await workUnitAsyncStorage.run(requestStore, () =>\n          actionHandler.apply(null, boundActionArguments)\n        )\n      } finally {\n        requestStore.phase = 'render'\n      }\n\n      // For form actions, we need to continue rendering the page.\n      if (isFetchAction) {\n        await executeRevalidates(workStore)\n        addRevalidationHeader(res, { workStore, requestStore })\n\n        actionResult = await finalizeAndGenerateFlight(req, ctx, requestStore, {\n          actionResult: Promise.resolve(returnVal),\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        })\n      }\n    })\n\n    return {\n      type: 'done',\n      result: actionResult,\n      formState,\n    }\n  } catch (err) {\n    if (isRedirectError(err)) {\n      const redirectUrl = getURLFromRedirectError(err)\n      const redirectType = getRedirectTypeFromError(err)\n\n      await executeRevalidates(workStore)\n      addRevalidationHeader(res, { workStore, requestStore })\n\n      // if it's a fetch action, we'll set the status code for logging/debugging purposes\n      // but we won't set a Location header, as the redirect will be handled by the client router\n      res.statusCode = RedirectStatusCode.SeeOther\n\n      if (isFetchAction) {\n        return {\n          type: 'done',\n          result: await createRedirectRenderResult(\n            req,\n            res,\n            host,\n            redirectUrl,\n            redirectType,\n            ctx.renderOpts.basePath,\n            workStore\n          ),\n        }\n      }\n\n      res.setHeader('Location', redirectUrl)\n      return {\n        type: 'done',\n        result: RenderResult.fromStatic(''),\n      }\n    } else if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n\n      await executeRevalidates(workStore)\n      addRevalidationHeader(res, { workStore, requestStore })\n\n      if (isFetchAction) {\n        const promise = Promise.reject(err)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            skipFlight: false,\n            actionResult: promise,\n            temporaryReferences,\n          }),\n        }\n      }\n      return {\n        type: 'not-found',\n      }\n    }\n\n    if (isFetchAction) {\n      res.statusCode = 500\n      await executeRevalidates(workStore)\n      const promise = Promise.reject(err)\n      try {\n        // we need to await the promise to trigger the rejection early\n        // so that it's already handled by the time we call\n        // the RSC runtime. Otherwise, it will throw an unhandled\n        // promise rejection error in the renderer.\n        await promise\n      } catch {\n        // swallow error, it's gonna be handled on the client\n      }\n\n      return {\n        type: 'done',\n        result: await generateFlight(req, ctx, requestStore, {\n          actionResult: promise,\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        }),\n      }\n    }\n\n    throw err\n  }\n}\n\n/**\n * Attempts to find the module ID for the action from the module map. When this fails, it could be a deployment skew where\n * the action came from a different deployment. It could also simply be an invalid POST request that is not a server action.\n * In either case, we'll throw an error to be handled by the caller.\n */\nfunction getActionModIdOrError(\n  actionId: string | null,\n  serverModuleMap: ServerModuleMap\n): string {\n  // if we're missing the action ID header, we can't do any further processing\n  if (!actionId) {\n    throw new InvariantError(\"Missing 'next-action' header.\")\n  }\n\n  const actionModId = serverModuleMap[actionId]?.id\n\n  if (!actionModId) {\n    throw new Error(\n      `Failed to find Server Action \"${actionId}\". This request might be from an older or newer deployment.\\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  return actionModId\n}\n"], "names": ["handleAction", "parseHostHeader", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "responseHeaders", "getHeaders", "responseCookies", "ResponseCookies", "fromNodeOutgoingHttpHeaders", "mergedHeaders", "filterReqHeaders", "actionsForbiddenHeaders", "getAll", "for<PERSON>ach", "cookie", "delete", "name", "set", "toString", "Headers", "addRevalidationHeader", "workStore", "requestStore", "isTagRevalidated", "pendingRevalidatedTags", "length", "isCookieRevalidated", "getModifiedCookieValues", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createForwardedActionResponse", "host", "workerPathname", "basePath", "Error", "forwardedHeaders", "proto", "incrementalCache", "requestProtocol", "origin", "process", "env", "__NEXT_PRIVATE_ORIGIN", "fetchUrl", "URL", "response", "body", "NEXT_RUNTIME", "isWebNextRequest", "isNodeNextRequest", "stream", "fetch", "method", "duplex", "redirect", "next", "internal", "get", "startsWith", "RSC_CONTENT_TYPE_HEADER", "includes", "FlightRenderResult", "cancel", "err", "console", "error", "RenderResult", "fromStatic", "getAppRelativeRedirectUrl", "redirectUrl", "parsedRedirectUrl", "pathname", "createRedirectRenderResult", "originalHost", "redirectType", "appRelativeRedirectUrl", "RSC_HEADER", "search", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "prerenderManifest", "preview", "previewModeId", "NEXT_ROUTER_STATE_TREE_HEADER", "ACTION_HEADER", "limitUntrustedHeaderValueForLogs", "slice", "originDomain", "forwarded<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedHostHeaderValue", "split", "trim", "<PERSON><PERSON><PERSON><PERSON>", "type", "ComponentMod", "serverModuleMap", "generateFlight", "serverActions", "ctx", "contentType", "serverActionsManifest", "page", "renderOpts", "actionId", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isPossibleServerAction", "getServerActionRequestMetadata", "isStaticGeneration", "temporaryReferences", "finalizeAndGenerateFlight", "args", "synchronizeMutableCookies", "isDraftMode", "draftMode", "isEnabled", "fetchCache", "warning", "warnBadServerActionRequest", "warn", "isCsrfOriginAllowed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "executeRevalidates", "promise", "Promise", "reject", "result", "actionResult", "skipFlight", "pathWasRevalidated", "boundActionArguments", "actionAsyncStorage", "formState", "actionModId", "actionWasForwarded", "Boolean", "forwarded<PERSON><PERSON><PERSON>", "selectWorkerForForwarding", "run", "isAction", "createTemporaryReferenceSet", "decodeReply", "decodeAction", "decodeFormState", "request", "action", "actionReturnedState", "phase", "workUnitAsyncStorage", "getActionModIdOrError", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "push", "actionData", "<PERSON><PERSON><PERSON>", "concat", "decodeReplyFromBusboy", "require", "Transform", "defaultBodySizeLimit", "bodySizeLimit", "bodySizeLimitBytes", "parse", "size", "pipe", "transform", "chunk", "encoding", "callback", "byteLength", "ApiError", "busboy", "def<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limits", "fieldSize", "fakeRequest", "Request", "ReadableStream", "start", "controller", "on", "enqueue", "Uint8Array", "close", "actionMod", "__next_app__", "actionHandler", "returnVal", "apply", "resolve", "isRedirectError", "getURLFromRedirectError", "getRedirectTypeFromError", "RedirectStatusCode", "<PERSON><PERSON><PERSON>", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "InvariantError", "id"], "mappings": ";;;;;;;;;;;;;;;IA8bsBA,YAAY;eAAZA;;IApDNC,eAAe;eAAfA;;;kCA9XT;oCAIA;0BAIA;+BAIA;qEACkB;oCAEU;uBAI5B;gCACiC;2BAKjC;yCACwC;gCACX;qBACf;yBAC2B;yBACjB;wBACa;6BACF;yBACU;oCACjB;8BACO;8CAEL;gCACN;mCACI;;;;;;AAEnC,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,GAAGV,OAAO;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAqB;IAErB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiB,IAAIC,uBAAc,CAACC,uBAAc,CAACC,IAAI,CAACJ;IAE9D,mCAAmC;IACnC,MAAMK,kBAAkBN,IAAIO,UAAU;IACtC,MAAMC,kBAAkB,IAAIC,wBAAe,CACzCC,IAAAA,mCAA2B,EAACJ;IAG9B,qCAAqC;IACrC,MAAMK,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,GAAGvB,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBiB,gBAAgB;IACzC,GACAO,8BAAuB;IAGzB,+EAA+E;IAC/E,kDAAkD;IAClDL,gBAAgBM,MAAM,GAAGC,OAAO,CAAC,CAACC;QAChC,IAAI,OAAOA,OAAO7B,KAAK,KAAK,aAAa;YACvCe,eAAee,MAAM,CAACD,OAAOE,IAAI;QACnC,OAAO;YACLhB,eAAeiB,GAAG,CAACH;QACrB;IACF;IAEA,qDAAqD;IACrDL,aAAa,CAAC,SAAS,GAAGT,eAAekB,QAAQ;IAEjD,8CAA8C;IAC9C,OAAOT,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIU,QAAQV;AACrB;AAEA,SAASW,sBACPtB,GAAqB,EACrB,EACEuB,SAAS,EACTC,YAAY,EAIb;QAewBD;IAbzB,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAME,mBAAmBF,EAAAA,oCAAAA,UAAUG,sBAAsB,qBAAhCH,kCAAkCI,MAAM,IAAG,IAAI;IACxE,MAAMC,sBAAsBC,IAAAA,uCAAuB,EACjDL,aAAaM,cAAc,EAC3BH,MAAM,GACJ,IACA;IAEJ3B,IAAI+B,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAER;QAAkBG;KAAoB;AAE9D;AAEA;;CAEC,GACD,eAAeM,8BACbnC,GAAoB,EACpBC,GAAqB,EACrBmC,IAAU,EACVC,cAAsB,EACtBC,QAAgB,EAChBd,SAAoB;QAeNA;IAbd,IAAI,CAACY,MAAM;QACT,MAAM,qBAEL,CAFK,IAAIG,MACR,8EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,mBAAmBzC,oBAAoBC,KAAKC;IAElD,sEAAsE;IACtE,+EAA+E;IAC/E,8CAA8C;IAC9CuC,iBAAiBpB,GAAG,CAAC,sBAAsB;IAE3C,MAAMqB,QAAQjB,EAAAA,8BAAAA,UAAUkB,gBAAgB,qBAA1BlB,4BAA4BmB,eAAe,KAAI;IAE7D,yEAAyE;IACzE,gDAAgD;IAChD,MAAMC,SAASC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGN,MAAM,GAAG,EAAEL,KAAKhD,KAAK,EAAE;IAE9E,MAAM4D,WAAW,IAAIC,IAAI,GAAGL,SAASN,WAAWD,gBAAgB;IAEhE,IAAI;YAsCAa;QArCF,IAAIC;QACJ,IACE,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAACrD,MACjB;YACA,IAAI,CAACA,IAAImD,IAAI,EAAE;gBACb,MAAM,qBAA6C,CAA7C,IAAIZ,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEAY,OAAOnD,IAAImD,IAAI;QACjB,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAACtD,MAClB;YACAmD,OAAOnD,IAAIuD,MAAM;QACnB,OAAO;YACL,MAAM,qBAA6C,CAA7C,IAAIhB,MAAM,qCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACpD;QAEA,wCAAwC;QACxC,MAAMW,WAAW,MAAMM,MAAMR,UAAU;YACrCS,QAAQ;YACRN;YACAO,QAAQ;YACRnE,SAASiD;YACTmB,UAAU;YACVC,MAAM;gBACJ,aAAa;gBACbC,UAAU;YACZ;QACF;QAEA,KACEX,wBAAAA,SAAS3D,OAAO,CAACuE,GAAG,CAAC,oCAArBZ,sBAAsCa,UAAU,CAACC,yCAAuB,GACxE;YACA,4EAA4E;YAC5E,KAAK,MAAM,CAAC7E,KAAKC,MAAM,IAAI8D,SAAS3D,OAAO,CAAE;gBAC3C,IAAI,CAACuB,8BAAuB,CAACmD,QAAQ,CAAC9E,MAAM;oBAC1Cc,IAAI+B,SAAS,CAAC7C,KAAKC;gBACrB;YACF;YAEA,OAAO,IAAI8E,sCAAkB,CAAChB,SAASC,IAAI;QAC7C,OAAO;gBACL,kFAAkF;YAClFD;aAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;QACvB;IACF,EAAE,OAAOC,KAAK;QACZ,oFAAoF;QACpFC,QAAQC,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAEF;IACrD;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAEA;;;;;;CAMC,GACD,SAASC,0BACPnC,QAAgB,EAChBF,IAAU,EACVsC,WAAmB;IAEnB,IAAIA,YAAYX,UAAU,CAAC,QAAQW,YAAYX,UAAU,CAAC,MAAM;QAC9D,2DAA2D;QAC3D,OAAO,IAAId,IAAI,GAAGX,WAAWoC,aAAa,EAAE;IAC9C;IAEA,MAAMC,oBAAoB,IAAI1B,IAAIyB;IAElC,IAAItC,CAAAA,wBAAAA,KAAMhD,KAAK,MAAKuF,kBAAkBvC,IAAI,EAAE;QAC1C,OAAO;IACT;IAEA,wDAAwD;IACxD,kDAAkD;IAClD,OAAOuC,kBAAkBC,QAAQ,CAACb,UAAU,CAACzB,YACzCqC,oBACA;AACN;AAEA,eAAeE,2BACb7E,GAAoB,EACpBC,GAAqB,EACrB6E,YAAkB,EAClBJ,WAAmB,EACnBK,YAA0B,EAC1BzC,QAAgB,EAChBd,SAAoB;IAEpBvB,IAAI+B,SAAS,CAAC,qBAAqB,GAAG0C,YAAY,CAAC,EAAEK,cAAc;IAEnE,2EAA2E;IAC3E,0EAA0E;IAC1E,+DAA+D;IAC/D,4EAA4E;IAC5E,sDAAsD;IACtD,MAAMC,yBAAyBP,0BAC7BnC,UACAwC,cACAJ;IAGF,IAAIM,wBAAwB;YAUZxD;QATd,IAAI,CAACsD,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIvC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,mBAAmBzC,oBAAoBC,KAAKC;QAClDuC,iBAAiBpB,GAAG,CAAC6D,4BAAU,EAAE;QAEjC,MAAMxC,QAAQjB,EAAAA,8BAAAA,UAAUkB,gBAAgB,qBAA1BlB,4BAA4BmB,eAAe,KAAI;QAE7D,yEAAyE;QACzE,gDAAgD;QAChD,MAAMC,SACJC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGN,MAAM,GAAG,EAAEqC,aAAa1F,KAAK,EAAE;QAEzE,MAAM4D,WAAW,IAAIC,IACnB,GAAGL,SAASoC,uBAAuBJ,QAAQ,GAAGI,uBAAuBE,MAAM,EAAE;QAG/E,IAAI1D,UAAUG,sBAAsB,EAAE;gBAOlCH,uDAAAA,+CAAAA;YANFgB,iBAAiBpB,GAAG,CAClB+D,6CAAkC,EAClC3D,UAAUG,sBAAsB,CAAC7B,IAAI,CAAC;YAExC0C,iBAAiBpB,GAAG,CAClBgE,iDAAsC,EACtC5D,EAAAA,+BAAAA,UAAUkB,gBAAgB,sBAA1BlB,gDAAAA,6BAA4B6D,iBAAiB,sBAA7C7D,wDAAAA,8CAA+C8D,OAAO,qBAAtD9D,sDAAwD+D,aAAa,KACnE;QAEN;QAEA,6FAA6F;QAC7F/C,iBAAiBtB,MAAM,CAACsE,+CAA6B;QACrD,uGAAuG;QACvG,wGAAwG;QACxGhD,iBAAiBtB,MAAM,CAACuE,+BAAa;QAErC,IAAI;gBAWAvC;YAVF,MAAMA,WAAW,MAAMM,MAAMR,UAAU;gBACrCS,QAAQ;gBACRlE,SAASiD;gBACToB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,KACEX,wBAAAA,SAAS3D,OAAO,CACbuE,GAAG,CAAC,oCADPZ,sBAEIa,UAAU,CAACC,yCAAuB,GACtC;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAAC7E,KAAKC,MAAM,IAAI8D,SAAS3D,OAAO,CAAE;oBAC3C,IAAI,CAACuB,8BAAuB,CAACmD,QAAQ,CAAC9E,MAAM;wBAC1Cc,IAAI+B,SAAS,CAAC7C,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAI8E,sCAAkB,CAAChB,SAASC,IAAI;YAC7C,OAAO;oBACL,kFAAkF;gBAClFD;iBAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;YACvB;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAkBA;;CAEC,GACD,SAASkB,iCAAiCtG,KAAa;IACrD,OAAOA,MAAMwC,MAAM,GAAG,MAAMxC,MAAMuG,KAAK,CAAC,GAAG,OAAO,QAAQvG;AAC5D;AAEO,SAASR,gBACdW,OAA4B,EAC5BqG,YAAqB;QAMfC,6BAAAA;IAJN,MAAMA,sBAAsBtG,OAAO,CAAC,mBAAmB;IACvD,MAAMuG,2BACJD,uBAAuBjG,MAAMC,OAAO,CAACgG,uBACjCA,mBAAmB,CAAC,EAAE,GACtBA,wCAAAA,6BAAAA,oBAAqBE,KAAK,CAAC,0BAA3BF,8BAAAA,0BAAiC,CAAC,EAAE,qBAApCA,4BAAsCG,IAAI;IAChD,MAAMC,aAAa1G,OAAO,CAAC,OAAO;IAElC,IAAIqG,cAAc;QAChB,OAAOE,6BAA6BF,eAChC;YACEM,IAAI;YACJ9G,OAAO0G;QACT,IACAG,eAAeL,eACb;YACEM,IAAI;YACJ9G,OAAO6G;QACT,IACAtG;IACR;IAEA,OAAOmG,2BACH;QACEI,IAAI;QACJ9G,OAAO0G;IACT,IACAG,aACE;QACEC,IAAI;QACJ9G,OAAO6G;IACT,IACAtG;AACR;AAgBO,eAAehB,aAAa,EACjCqB,GAAG,EACHC,GAAG,EACHkG,YAAY,EACZC,eAAe,EACfC,cAAc,EACd7E,SAAS,EACTC,YAAY,EACZ6E,aAAa,EACbC,GAAG,EAWJ;IAWC,MAAMC,cAAcxG,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM,EAAEkH,qBAAqB,EAAEC,IAAI,EAAE,GAAGH,IAAII,UAAU;IAEtD,MAAM,EACJC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,sBAAsB,EACvB,GAAGC,IAAAA,uDAA8B,EAACjH;IAEnC,iDAAiD;IACjD,2FAA2F;IAC3F,kFAAkF;IAClF,IAAI,CAACgH,wBAAwB;QAC3B;IACF;IAEA,IAAIxF,UAAU0F,kBAAkB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAI3E,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI4E;IAEJ,MAAMC,4BAA4C,CAAC,GAAGC;QACpD,4DAA4D;QAC5D,8EAA8E;QAC9E,mFAAmF;QACnF,qEAAqE;QACrEC,IAAAA,uCAAyB,EAAC7F;QAE1B,yEAAyE;QACzE,oEAAoE;QACpED,UAAU+F,WAAW,GAAG9F,aAAa+F,SAAS,CAACC,SAAS;QAExD,OAAOpB,kBAAkBgB;IAC3B;IAEA,qFAAqF;IACrF7F,UAAUkG,UAAU,GAAG;IAEvB,MAAM9B,eACJ,OAAO5F,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAI0D,IAAIjD,IAAIT,OAAO,CAAC,SAAS,EAAE6C,IAAI,GACnCzC;IACN,MAAMyC,OAAOxD,gBAAgBoB,IAAIT,OAAO;IAExC,IAAIoI,UAA8BhI;IAElC,SAASiI;QACP,IAAID,SAAS;YACXE,IAAAA,SAAI,EAACF;QACP;IACF;IACA,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC/B,cAAc;QACjB,0EAA0E;QAC1E,aAAa;QACb+B,UAAU;IACZ,OAAO,IAAI,CAACvF,QAAQwD,iBAAiBxD,KAAKhD,KAAK,EAAE;QAC/C,2EAA2E;QAC3E,2EAA2E;QAC3E,uCAAuC;QACvC,IAAI0I,IAAAA,mCAAmB,EAAClC,cAAcU,iCAAAA,cAAeyB,cAAc,GAAG;QACpE,YAAY;QACd,OAAO;YACL,IAAI3F,MAAM;gBACR,qEAAqE;gBACrEiC,QAAQC,KAAK,CACX,CAAC,EAAE,EACDlC,KAAK8D,IAAI,CACV,uBAAuB,EAAER,iCACxBtD,KAAKhD,KAAK,EACV,iDAAiD,EAAEsG,iCACnDE,cACA,gEAAgE,CAAC;YAEvE,OAAO;gBACL,uDAAuD;gBACvDvB,QAAQC,KAAK,CACX,CAAC,gLAAgL,CAAC;YAEtL;YAEA,MAAMA,QAAQ,qBAA4C,CAA5C,IAAI/B,MAAM,oCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA2C;YAEzD,IAAIwE,eAAe;gBACjB9G,IAAI+H,UAAU,GAAG;gBACjB,MAAMC,IAAAA,qCAAkB,EAACzG;gBAEzB,MAAM0G,UAAUC,QAAQC,MAAM,CAAC9D;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAM4D;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBAEA,OAAO;oBACLhC,MAAM;oBACNmC,QAAQ,MAAMjB,0BAA0BpH,KAAKuG,KAAK9E,cAAc;wBAC9D6G,cAAcJ;wBACd,6EAA6E;wBAC7EK,YAAY,CAAC/G,UAAUgH,kBAAkB;wBACzCrB;oBACF;gBACF;YACF;YAEA,MAAM7C;QACR;IACF;IAEA,sDAAsD;IACtDrE,IAAI+B,SAAS,CACX,iBACA;IAGF,IAAIyG,uBAAkC,EAAE;IAExC,MAAM,EAAEC,kBAAkB,EAAE,GAAGvC;IAE/B,IAAImC;IACJ,IAAIK;IACJ,IAAIC;IACJ,MAAMC,qBAAqBC,QAAQ9I,IAAIT,OAAO,CAAC,qBAAqB;IAEpE,IAAIqH,UAAU;QACZ,MAAMmC,kBAAkBC,IAAAA,sCAAyB,EAC/CpC,UACAF,MACAD;QAGF,6EAA6E;QAC7E,qFAAqF;QACrF,IAAIsC,iBAAiB;YACnB,OAAO;gBACL7C,MAAM;gBACNmC,QAAQ,MAAMlG,8BACZnC,KACAC,KACAmC,MACA2G,iBACAxC,IAAII,UAAU,CAACrE,QAAQ,EACvBd;YAEJ;QACF;IACF;IAEA,IAAI;QACF,MAAMkH,mBAAmBO,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IACE,qEAAqE;YACrE,6DAA6D;YAC7DrG,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAACrD,MACjB;gBACA,IAAI,CAACA,IAAImD,IAAI,EAAE;oBACb,MAAM,qBAA6C,CAA7C,IAAIZ,MAAM,qCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;gBAEA,uBAAuB;gBAEvB,2CAA2C;gBAC3C,MAAM,EACJ4G,2BAA2B,EAC3BC,WAAW,EACXC,YAAY,EACZC,eAAe,EAChB,GAAGnD;gBAEJgB,sBAAsBgC;gBAEtB,IAAIrC,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAM7H,WAAW,MAAMe,IAAIuJ,OAAO,CAACtK,QAAQ;oBAC3C,IAAI8H,eAAe;wBACjB0B,uBAAuB,MAAMW,YAC3BnK,UACAmH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,MAAMqC,SAAS,MAAMH,aAAapK,UAAUmH;wBAC5C,IAAI,OAAOoD,WAAW,YAAY;4BAChC,4EAA4E;4BAC5E5B;4BAEA,IAAI6B;4BACJhI,aAAaiI,KAAK,GAAG;4BACrB,IAAI;gCACFD,sBAAsB,MAAME,kDAAoB,CAACV,GAAG,CAClDxH,cACA+H;4BAEJ,SAAU;gCACR/H,aAAaiI,KAAK,GAAG;4BACvB;4BAEAf,YAAY,MAAMW,gBAChBG,qBACAxK,UACAmH;wBAEJ;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFwC,cAAcgB,sBAAsBhD,UAAUR;oBAChD,EAAE,OAAOhC,KAAK;wBACZ,IAAIwC,aAAa,MAAM;4BACrBvC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACL8B,MAAM;wBACR;oBACF;oBAEA,MAAM2D,SAAmB,EAAE;oBAC3B,MAAMC,SAAS9J,IAAImD,IAAI,CAAC4G,SAAS;oBACjC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAE5K,KAAK,EAAE,GAAG,MAAM0K,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,OAAOK,IAAI,CAAC9K;oBACd;oBAEA,MAAM+K,aAAaC,OAAOC,MAAM,CAACR,QAAQxI,QAAQ,CAAC;oBAElD,IAAIwF,oBAAoB;wBACtB,MAAM5H,WAAWJ,8BAA8BsL;wBAC/C1B,uBAAuB,MAAMW,YAC3BnK,UACAmH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLsB,uBAAuB,MAAMW,YAC3Be,YACA/D,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO,IACL,qEAAqE;YACrE,6DAA6D;YAC7DtE,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAACtD,MAClB;gBACA,oEAAoE;gBACpE,MAAM,EACJmJ,2BAA2B,EAC3BC,WAAW,EACXkB,qBAAqB,EACrBjB,YAAY,EACZC,eAAe,EAChB,GAAGiB,QACF,CAAC,mBAAmB,CAAC;gBAGvBpD,sBAAsBgC;gBAEtB,MAAM,EAAEqB,SAAS,EAAE,GACjBD,QAAQ;gBAEV,MAAME,uBAAuB;gBAC7B,MAAMC,gBACJpE,CAAAA,iCAAAA,cAAeoE,aAAa,KAAID;gBAClC,MAAME,qBACJD,kBAAkBD,uBACd,AACEF,QAAQ,4BACRK,KAAK,CAACF,iBACR,OAAO,KAAK,OAAO;;gBAEzB,IAAIG,OAAO;gBACX,MAAM1H,OAAOnD,IAAImD,IAAI,CAAC2H,IAAI,CACxB,IAAIN,UAAU;oBACZO,WAAUC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ;wBACjCL,QAAQT,OAAOe,UAAU,CAACH,OAAOC;wBACjC,IAAIJ,OAAOF,oBAAoB;4BAC7B,MAAM,EAAES,QAAQ,EAAE,GAAGb,QAAQ;4BAE7BW,SACE,qBAIC,CAJD,IAAIE,SACF,KACA,CAAC,cAAc,EAAEV,cAAc;8JAC2G,CAAC,GAH7I,qBAAA;uCAAA;4CAAA;8CAAA;4BAIA;4BAEF;wBACF;wBAEAQ,SAAS,MAAMF;oBACjB;gBACF;gBAGF,IAAIlE,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAMsE,SAAS,AAACd,QAAQ,UAAsC;4BAC5De,iBAAiB;4BACjB/L,SAASS,IAAIT,OAAO;4BACpBgM,QAAQ;gCAAEC,WAAWb;4BAAmB;wBAC1C;wBAEAxH,KAAK2H,IAAI,CAACO;wBAEV5C,uBAAuB,MAAM6B,sBAC3Be,QACAjF,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAMsE,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDjI,QAAQ;4BACR,mBAAmB;4BACnBlE,SAAS;gCAAE,gBAAgBiH;4BAAY;4BACvCrD,MAAM,IAAIwI,eAAe;gCACvBC,OAAO,CAACC;oCACN1I,KAAK2I,EAAE,CAAC,QAAQ,CAACd;wCACfa,WAAWE,OAAO,CAAC,IAAIC,WAAWhB;oCACpC;oCACA7H,KAAK2I,EAAE,CAAC,OAAO;wCACbD,WAAWI,KAAK;oCAClB;oCACA9I,KAAK2I,EAAE,CAAC,SAAS,CAAC1H;wCAChByH,WAAWvH,KAAK,CAACF;oCACnB;gCACF;4BACF;4BACAV,QAAQ;wBACV;wBACA,MAAMzE,WAAW,MAAMwM,YAAYxM,QAAQ;wBAC3C,MAAMuK,SAAS,MAAMH,aAAapK,UAAUmH;wBAC5C,IAAI,OAAOoD,WAAW,YAAY;4BAChC,4EAA4E;4BAC5E5B;4BAEA,IAAI6B;4BACJhI,aAAaiI,KAAK,GAAG;4BACrB,IAAI;gCACFD,sBAAsB,MAAME,kDAAoB,CAACV,GAAG,CAClDxH,cACA+H;4BAEJ,SAAU;gCACR/H,aAAaiI,KAAK,GAAG;4BACvB;4BAEAf,YAAY,MAAMW,gBAChBG,qBACAxK,UACAmH;wBAEJ;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFwC,cAAcgB,sBAAsBhD,UAAUR;oBAChD,EAAE,OAAOhC,KAAK;wBACZ,IAAIwC,aAAa,MAAM;4BACrBvC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACL8B,MAAM;wBACR;oBACF;oBAEA,MAAM2D,SAAmB,EAAE;oBAC3B,WAAW,MAAMmB,SAAShL,IAAImD,IAAI,CAAE;wBAClC0G,OAAOK,IAAI,CAACE,OAAO9J,IAAI,CAAC0K;oBAC1B;oBAEA,MAAMb,aAAaC,OAAOC,MAAM,CAACR,QAAQxI,QAAQ,CAAC;oBAElD,IAAIwF,oBAAoB;wBACtB,MAAM5H,WAAWJ,8BAA8BsL;wBAC/C1B,uBAAuB,MAAMW,YAC3BnK,UACAmH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLsB,uBAAuB,MAAMW,YAC3Be,YACA/D,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO;gBACL,MAAM,qBAA6C,CAA7C,IAAI5E,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAI;gBACFqG,cACEA,eAAegB,sBAAsBhD,UAAUR;YACnD,EAAE,OAAOhC,KAAK;gBACZ,IAAIwC,aAAa,MAAM;oBACrBvC,QAAQC,KAAK,CAACF;gBAChB;gBACA,OAAO;oBACL8B,MAAM;gBACR;YACF;YAEA,MAAMgG,YAAa,MAAM/F,aAAagG,YAAY,CAAC5B,OAAO,CACxD3B;YAEF,MAAMwD,gBACJF,SAAS,CACP,yFAAyF;YACzFtF,SACD;YAEH,IAAIyF;YACJ5K,aAAaiI,KAAK,GAAG;YACrB,IAAI;gBACF2C,YAAY,MAAM1C,kDAAoB,CAACV,GAAG,CAACxH,cAAc,IACvD2K,cAAcE,KAAK,CAAC,MAAM7D;YAE9B,SAAU;gBACRhH,aAAaiI,KAAK,GAAG;YACvB;YAEA,4DAA4D;YAC5D,IAAI3C,eAAe;gBACjB,MAAMkB,IAAAA,qCAAkB,EAACzG;gBACzBD,sBAAsBtB,KAAK;oBAAEuB;oBAAWC;gBAAa;gBAErD6G,eAAe,MAAMlB,0BAA0BpH,KAAKuG,KAAK9E,cAAc;oBACrE6G,cAAcH,QAAQoE,OAAO,CAACF;oBAC9B,iIAAiI;oBACjI9D,YAAY,CAAC/G,UAAUgH,kBAAkB,IAAIK;oBAC7C1B;gBACF;YACF;QACF;QAEA,OAAO;YACLjB,MAAM;YACNmC,QAAQC;YACRK;QACF;IACF,EAAE,OAAOvE,KAAK;QACZ,IAAIoI,IAAAA,8BAAe,EAACpI,MAAM;YACxB,MAAMM,cAAc+H,IAAAA,iCAAuB,EAACrI;YAC5C,MAAMW,eAAe2H,IAAAA,kCAAwB,EAACtI;YAE9C,MAAM6D,IAAAA,qCAAkB,EAACzG;YACzBD,sBAAsBtB,KAAK;gBAAEuB;gBAAWC;YAAa;YAErD,mFAAmF;YACnF,2FAA2F;YAC3FxB,IAAI+H,UAAU,GAAG2E,sCAAkB,CAACC,QAAQ;YAE5C,IAAI7F,eAAe;gBACjB,OAAO;oBACLb,MAAM;oBACNmC,QAAQ,MAAMxD,2BACZ7E,KACAC,KACAmC,MACAsC,aACAK,cACAwB,IAAII,UAAU,CAACrE,QAAQ,EACvBd;gBAEJ;YACF;YAEAvB,IAAI+B,SAAS,CAAC,YAAY0C;YAC1B,OAAO;gBACLwB,MAAM;gBACNmC,QAAQ9D,qBAAY,CAACC,UAAU,CAAC;YAClC;QACF,OAAO,IAAIqI,IAAAA,6CAAyB,EAACzI,MAAM;YACzCnE,IAAI+H,UAAU,GAAG8E,IAAAA,+CAA2B,EAAC1I;YAE7C,MAAM6D,IAAAA,qCAAkB,EAACzG;YACzBD,sBAAsBtB,KAAK;gBAAEuB;gBAAWC;YAAa;YAErD,IAAIsF,eAAe;gBACjB,MAAMmB,UAAUC,QAAQC,MAAM,CAAChE;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAM8D;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBACA,OAAO;oBACLhC,MAAM;oBACNmC,QAAQ,MAAMjB,0BAA0BpH,KAAKuG,KAAK9E,cAAc;wBAC9D8G,YAAY;wBACZD,cAAcJ;wBACdf;oBACF;gBACF;YACF;YACA,OAAO;gBACLjB,MAAM;YACR;QACF;QAEA,IAAIa,eAAe;YACjB9G,IAAI+H,UAAU,GAAG;YACjB,MAAMC,IAAAA,qCAAkB,EAACzG;YACzB,MAAM0G,UAAUC,QAAQC,MAAM,CAAChE;YAC/B,IAAI;gBACF,8DAA8D;gBAC9D,mDAAmD;gBACnD,yDAAyD;gBACzD,2CAA2C;gBAC3C,MAAM8D;YACR,EAAE,OAAM;YACN,qDAAqD;YACvD;YAEA,OAAO;gBACLhC,MAAM;gBACNmC,QAAQ,MAAMhC,eAAerG,KAAKuG,KAAK9E,cAAc;oBACnD6G,cAAcJ;oBACd,iIAAiI;oBACjIK,YAAY,CAAC/G,UAAUgH,kBAAkB,IAAIK;oBAC7C1B;gBACF;YACF;QACF;QAEA,MAAM/C;IACR;AACF;AAEA;;;;CAIC,GACD,SAASwF,sBACPhD,QAAuB,EACvBR,eAAgC;QAOZA;IALpB,4EAA4E;IAC5E,IAAI,CAACQ,UAAU;QACb,MAAM,qBAAmD,CAAnD,IAAImG,8BAAc,CAAC,kCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,MAAMnE,eAAcxC,4BAAAA,eAAe,CAACQ,SAAS,qBAAzBR,0BAA2B4G,EAAE;IAEjD,IAAI,CAACpE,aAAa;QAChB,MAAM,qBAEL,CAFK,IAAIrG,MACR,CAAC,8BAA8B,EAAEqE,SAAS,qIAAqI,CAAC,GAD5K,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOgC;AACT"}