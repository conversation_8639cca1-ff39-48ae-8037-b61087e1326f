{"version": 3, "sources": ["../../../src/server/app-render/cache-signal.ts"], "sourcesContent": ["/**\n * This class is used to detect when all cache reads for a given render are settled.\n * We do this to allow for cache warming the prerender without having to continue rendering\n * the remainder of the page. This feature is really only useful when the dynamicIO flag is on\n * and should only be used in codepaths gated with this feature.\n */\n\nexport class CacheSignal {\n  private count: number\n  private earlyListeners: Array<() => void>\n  private listeners: Array<() => void>\n  private tickPending: boolean\n  private taskPending: boolean\n\n  constructor() {\n    this.count = 0\n    this.earlyListeners = []\n    this.listeners = []\n    this.tickPending = false\n    this.taskPending = false\n  }\n\n  private noMorePendingCaches() {\n    if (!this.tickPending) {\n      this.tickPending = true\n      process.nextTick(() => {\n        this.tickPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.earlyListeners.length; i++) {\n            this.earlyListeners[i]()\n          }\n          this.earlyListeners.length = 0\n        }\n      })\n    }\n    if (!this.taskPending) {\n      this.taskPending = true\n      setTimeout(() => {\n        this.taskPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.listeners.length; i++) {\n            this.listeners[i]()\n          }\n          this.listeners.length = 0\n        }\n      }, 0)\n    }\n  }\n\n  /**\n   * This promise waits until there are no more in progress cache reads but no later.\n   * This allows for adding more cache reads after to delay cacheReady.\n   */\n  inputReady() {\n    return new Promise<void>((resolve) => {\n      this.earlyListeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  /**\n   * If there are inflight cache reads this Promise can resolve in a microtask however\n   * if there are no inflight cache reads then we wait at least one task to allow initial\n   * cache reads to be initiated.\n   */\n  cacheReady() {\n    return new Promise<void>((resolve) => {\n      this.listeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  beginRead() {\n    this.count++\n  }\n\n  endRead() {\n    // If this is the last read we need to wait a task before we can claim the cache is settled.\n    // The cache read will likely ping a Server Component which can read from the cache again and this\n    // will play out in a microtask so we need to only resolve pending listeners if we're still at 0\n    // after at least one task.\n    // We only want one task scheduled at a time so when we hit count 1 we don't decrement the counter immediately.\n    // If intervening reads happen before the scheduled task runs they will never observe count 1 preventing reentrency\n    this.count--\n    if (this.count === 0) {\n      this.noMorePendingCaches()\n    }\n  }\n}\n"], "names": ["CacheSignal", "constructor", "count", "earlyListeners", "listeners", "tickPending", "taskPending", "noMorePendingCaches", "process", "nextTick", "i", "length", "setTimeout", "inputReady", "Promise", "resolve", "push", "cacheReady", "beginRead", "endRead"], "mappings": "AAAA;;;;;CAKC;;;;+BAEYA;;;eAAAA;;;AAAN,MAAMA;IAOXC,aAAc;QACZ,IAAI,CAACC,KAAK,GAAG;QACb,IAAI,CAACC,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,WAAW,GAAG;QACnB,IAAI,CAACC,WAAW,GAAG;IACrB;IAEQC,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;YACrB,IAAI,CAACA,WAAW,GAAG;YACnBG,QAAQC,QAAQ,CAAC;gBACf,IAAI,CAACJ,WAAW,GAAG;gBACnB,IAAI,IAAI,CAACH,KAAK,KAAK,GAAG;oBACpB,IAAK,IAAIQ,IAAI,GAAGA,IAAI,IAAI,CAACP,cAAc,CAACQ,MAAM,EAAED,IAAK;wBACnD,IAAI,CAACP,cAAc,CAACO,EAAE;oBACxB;oBACA,IAAI,CAACP,cAAc,CAACQ,MAAM,GAAG;gBAC/B;YACF;QACF;QACA,IAAI,CAAC,IAAI,CAACL,WAAW,EAAE;YACrB,IAAI,CAACA,WAAW,GAAG;YACnBM,WAAW;gBACT,IAAI,CAACN,WAAW,GAAG;gBACnB,IAAI,IAAI,CAACJ,KAAK,KAAK,GAAG;oBACpB,IAAK,IAAIQ,IAAI,GAAGA,IAAI,IAAI,CAACN,SAAS,CAACO,MAAM,EAAED,IAAK;wBAC9C,IAAI,CAACN,SAAS,CAACM,EAAE;oBACnB;oBACA,IAAI,CAACN,SAAS,CAACO,MAAM,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA;;;GAGC,GACDE,aAAa;QACX,OAAO,IAAIC,QAAc,CAACC;YACxB,IAAI,CAACZ,cAAc,CAACa,IAAI,CAACD;YACzB,IAAI,IAAI,CAACb,KAAK,KAAK,GAAG;gBACpB,IAAI,CAACK,mBAAmB;YAC1B;QACF;IACF;IAEA;;;;GAIC,GACDU,aAAa;QACX,OAAO,IAAIH,QAAc,CAACC;YACxB,IAAI,CAACX,SAAS,CAACY,IAAI,CAACD;YACpB,IAAI,IAAI,CAACb,KAAK,KAAK,GAAG;gBACpB,IAAI,CAACK,mBAAmB;YAC1B;QACF;IACF;IAEAW,YAAY;QACV,IAAI,CAAChB,KAAK;IACZ;IAEAiB,UAAU;QACR,4FAA4F;QAC5F,kGAAkG;QAClG,gGAAgG;QAChG,2BAA2B;QAC3B,+GAA+G;QAC/G,mHAAmH;QACnH,IAAI,CAACjB,KAAK;QACV,IAAI,IAAI,CAACA,KAAK,KAAK,GAAG;YACpB,IAAI,CAACK,mBAAmB;QAC1B;IACF;AACF"}