{"version": 3, "sources": ["../../../src/server/app-render/get-segment-param.tsx"], "sourcesContent": ["import { INTERCEPTION_ROUTE_MARKERS } from '../../shared/lib/router/utils/interception-routes'\nimport type { DynamicParamTypes } from './types'\n\n/**\n * Parse dynamic route segment to type of parameter\n */\nexport function getSegmentParam(segment: string): {\n  param: string\n  type: DynamicParamTypes\n} | null {\n  const interceptionMarker = INTERCEPTION_ROUTE_MARKERS.find((marker) =>\n    segment.startsWith(marker)\n  )\n\n  // if an interception marker is part of the path segment, we need to jump ahead\n  // to the relevant portion for param parsing\n  if (interceptionMarker) {\n    segment = segment.slice(interceptionMarker.length)\n  }\n\n  if (segment.startsWith('[[...') && segment.endsWith(']]')) {\n    return {\n      // TODO-APP: Optional catchall does not currently work with parallel routes,\n      // so for now aren't handling a potential interception marker.\n      type: 'optional-catchall',\n      param: segment.slice(5, -2),\n    }\n  }\n\n  if (segment.startsWith('[...') && segment.endsWith(']')) {\n    return {\n      type: interceptionMarker ? 'catchall-intercepted' : 'catchall',\n      param: segment.slice(4, -1),\n    }\n  }\n\n  if (segment.startsWith('[') && segment.endsWith(']')) {\n    return {\n      type: interceptionMarker ? 'dynamic-intercepted' : 'dynamic',\n      param: segment.slice(1, -1),\n    }\n  }\n\n  return null\n}\n"], "names": ["getSegmentParam", "segment", "<PERSON><PERSON><PERSON><PERSON>", "INTERCEPTION_ROUTE_MARKERS", "find", "marker", "startsWith", "slice", "length", "endsWith", "type", "param"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;oCAN2B;AAMpC,SAASA,gBAAgBC,OAAe;IAI7C,MAAMC,qBAAqBC,8CAA0B,CAACC,IAAI,CAAC,CAACC,SAC1DJ,QAAQK,UAAU,CAACD;IAGrB,+EAA+E;IAC/E,4CAA4C;IAC5C,IAAIH,oBAAoB;QACtBD,UAAUA,QAAQM,KAAK,CAACL,mBAAmBM,MAAM;IACnD;IAEA,IAAIP,QAAQK,UAAU,CAAC,YAAYL,QAAQQ,QAAQ,CAAC,OAAO;QACzD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9DC,MAAM;YACNC,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,IAAIN,QAAQK,UAAU,CAAC,WAAWL,QAAQQ,QAAQ,CAAC,MAAM;QACvD,OAAO;YACLC,MAAMR,qBAAqB,yBAAyB;YACpDS,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,IAAIN,QAAQK,UAAU,CAAC,QAAQL,QAAQQ,QAAQ,CAAC,MAAM;QACpD,OAAO;YACLC,MAAMR,qBAAqB,wBAAwB;YACnDS,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,OAAO;AACT"}