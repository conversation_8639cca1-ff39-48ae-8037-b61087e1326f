{"version": 3, "sources": ["../../../../src/server/api-utils/node/api-resolver.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiRequest, NextApiResponse } from '../../../shared/lib/utils'\nimport type { PageConfig, ResponseLimit } from '../../../types'\nimport type { __ApiPreviewProps } from '../.'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { ServerOnInstrumentationRequestError } from '../../app-render/types'\n\nimport bytes from 'next/dist/compiled/bytes'\nimport { generateETag } from '../../lib/etag'\nimport { sendEtagResponse } from '../../send-payload'\nimport { Stream } from 'stream'\nimport isError from '../../../lib/is-error'\nimport { isResSent } from '../../../shared/lib/utils'\nimport { interopDefault } from '../../../lib/interop-default'\nimport {\n  setLazyProp,\n  sendStatusCode,\n  redirect,\n  clearPreviewData,\n  sendError,\n  A<PERSON><PERSON><PERSON>r,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  RESPONSE_LIMIT_DEFAULT,\n} from './../index'\nimport { getCookieParser } from './../get-cookie-parser'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../../lib/constants'\nimport { tryGetPreviewData } from './try-get-preview-data'\nimport { parseBody } from './parse-body'\n\ntype RevalidateFn = (config: {\n  urlPath: string\n  revalidateHeaders: { [key: string]: string | string[] }\n  opts: { unstable_onlyGenerated?: boolean }\n}) => Promise<void>\n\ntype ApiContext = __ApiPreviewProps & {\n  trustHostHeader?: boolean\n  allowedRevalidateHeaderKeys?: string[]\n  hostname?: string\n  revalidate?: RevalidateFn\n  multiZoneDraftMode?: boolean\n  dev: boolean\n}\n\nfunction getMaxContentLength(responseLimit?: ResponseLimit) {\n  if (responseLimit && typeof responseLimit !== 'boolean') {\n    return bytes.parse(responseLimit)\n  }\n  return RESPONSE_LIMIT_DEFAULT\n}\n\n/**\n * Send `any` body to response\n * @param req request object\n * @param res response object\n * @param body of response\n */\nfunction sendData(req: NextApiRequest, res: NextApiResponse, body: any): void {\n  if (body === null || body === undefined) {\n    res.end()\n    return\n  }\n\n  // strip irrelevant headers/body\n  if (res.statusCode === 204 || res.statusCode === 304) {\n    res.removeHeader('Content-Type')\n    res.removeHeader('Content-Length')\n    res.removeHeader('Transfer-Encoding')\n\n    if (process.env.NODE_ENV === 'development' && body) {\n      console.warn(\n        `A body was attempted to be set with a 204 statusCode for ${req.url}, this is invalid and the body was ignored.\\n` +\n          `See more info here https://nextjs.org/docs/messages/invalid-api-status-body`\n      )\n    }\n    res.end()\n    return\n  }\n\n  const contentType = res.getHeader('Content-Type')\n\n  if (body instanceof Stream) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    body.pipe(res)\n    return\n  }\n\n  const isJSONLike = ['object', 'number', 'boolean'].includes(typeof body)\n  const stringifiedBody = isJSONLike ? JSON.stringify(body) : body\n  const etag = generateETag(stringifiedBody)\n  if (sendEtagResponse(req, res, etag)) {\n    return\n  }\n\n  if (Buffer.isBuffer(body)) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    res.setHeader('Content-Length', body.length)\n    res.end(body)\n    return\n  }\n\n  if (isJSONLike) {\n    res.setHeader('Content-Type', 'application/json; charset=utf-8')\n  }\n\n  res.setHeader('Content-Length', Buffer.byteLength(stringifiedBody))\n  res.end(stringifiedBody)\n}\n\n/**\n * Send `JSON` object\n * @param res response object\n * @param jsonBody of data\n */\nfunction sendJson(res: NextApiResponse, jsonBody: any): void {\n  // Set header to application/json\n  res.setHeader('Content-Type', 'application/json; charset=utf-8')\n\n  // Use send to handle request\n  res.send(JSON.stringify(jsonBody))\n}\n\nfunction isValidData(str: any): str is string {\n  return typeof str === 'string' && str.length >= 16\n}\n\nfunction setDraftMode<T>(\n  res: NextApiResponse<T>,\n  options: {\n    enable: boolean\n    previewModeId?: string\n  }\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  const expires = options.enable ? undefined : new Date(0)\n  // To delete a cookie, set `expires` to a date in the past:\n  // https://tools.ietf.org/html/rfc6265#section-4.1.1\n  // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      expires,\n    }),\n  ])\n  return res\n}\n\nfunction setPreviewData<T>(\n  res: NextApiResponse<T>,\n  data: object | string, // TODO: strict runtime type checking\n  options: {\n    maxAge?: number\n    path?: string\n  } & __ApiPreviewProps\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  if (!isValidData(options.previewModeEncryptionKey)) {\n    throw new Error('invariant: invalid previewModeEncryptionKey')\n  }\n  if (!isValidData(options.previewModeSigningKey)) {\n    throw new Error('invariant: invalid previewModeSigningKey')\n  }\n\n  const jsonwebtoken =\n    require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n  const { encryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const payload = jsonwebtoken.sign(\n    {\n      data: encryptWithSecret(\n        Buffer.from(options.previewModeEncryptionKey),\n        JSON.stringify(data)\n      ),\n    },\n    options.previewModeSigningKey,\n    {\n      algorithm: 'HS256',\n      ...(options.maxAge !== undefined\n        ? { expiresIn: options.maxAge }\n        : undefined),\n    }\n  )\n\n  // limit preview mode cookie to 2KB since we shouldn't store too much\n  // data here and browsers drop cookies over 4KB\n  if (payload.length > 2048) {\n    throw new Error(\n      `Preview data is limited to 2KB currently, reduce how much data you are storing as preview data to continue`\n    )\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, payload, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n  return res\n}\n\nasync function revalidate(\n  urlPath: string,\n  opts: {\n    unstable_onlyGenerated?: boolean\n  },\n  req: IncomingMessage,\n  context: ApiContext\n) {\n  if (typeof urlPath !== 'string' || !urlPath.startsWith('/')) {\n    throw new Error(\n      `Invalid urlPath provided to revalidate(), must be a path e.g. /blog/post-1, received ${urlPath}`\n    )\n  }\n  const revalidateHeaders: HeadersInit = {\n    [PRERENDER_REVALIDATE_HEADER]: context.previewModeId,\n    ...(opts.unstable_onlyGenerated\n      ? {\n          [PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER]: '1',\n        }\n      : {}),\n  }\n  const allowedRevalidateHeaderKeys = [\n    ...(context.allowedRevalidateHeaderKeys || []),\n  ]\n\n  if (context.trustHostHeader || context.dev) {\n    allowedRevalidateHeaderKeys.push('cookie')\n  }\n\n  if (context.trustHostHeader) {\n    allowedRevalidateHeaderKeys.push('x-vercel-protection-bypass')\n  }\n\n  for (const key of Object.keys(req.headers)) {\n    if (allowedRevalidateHeaderKeys.includes(key)) {\n      revalidateHeaders[key] = req.headers[key] as string\n    }\n  }\n\n  try {\n    if (context.trustHostHeader) {\n      const res = await fetch(`https://${req.headers.host}${urlPath}`, {\n        method: 'HEAD',\n        headers: revalidateHeaders,\n      })\n      // we use the cache header to determine successful revalidate as\n      // a non-200 status code can be returned from a successful revalidate\n      // e.g. notFound: true returns 404 status code but is successful\n      const cacheHeader =\n        res.headers.get('x-vercel-cache') || res.headers.get('x-nextjs-cache')\n\n      if (\n        cacheHeader?.toUpperCase() !== 'REVALIDATED' &&\n        res.status !== 200 &&\n        !(res.status === 404 && opts.unstable_onlyGenerated)\n      ) {\n        throw new Error(`Invalid response ${res.status}`)\n      }\n    } else if (context.revalidate) {\n      await context.revalidate({\n        urlPath,\n        revalidateHeaders,\n        opts,\n      })\n    } else {\n      throw new Error(\n        `Invariant: required internal revalidate method not passed to api-utils`\n      )\n    }\n  } catch (err: unknown) {\n    throw new Error(\n      `Failed to revalidate ${urlPath}: ${isError(err) ? err.message : err}`\n    )\n  }\n}\n\nexport async function apiResolver(\n  req: IncomingMessage,\n  res: ServerResponse,\n  query: any,\n  resolverModule: any,\n  apiContext: ApiContext,\n  propagateError: boolean,\n  dev?: boolean,\n  page?: string,\n  onError?: ServerOnInstrumentationRequestError\n): Promise<void> {\n  const apiReq = req as NextApiRequest\n  const apiRes = res as NextApiResponse\n\n  try {\n    if (!resolverModule) {\n      res.statusCode = 404\n      res.end('Not Found')\n      return\n    }\n    const config: PageConfig = resolverModule.config || {}\n    const bodyParser = config.api?.bodyParser !== false\n    const responseLimit = config.api?.responseLimit ?? true\n    const externalResolver = config.api?.externalResolver || false\n\n    // Parsing of cookies\n    setLazyProp({ req: apiReq }, 'cookies', getCookieParser(req.headers))\n    // Parsing query string\n    apiReq.query = query\n    // Parsing preview data\n    setLazyProp({ req: apiReq }, 'previewData', () =>\n      tryGetPreviewData(req, res, apiContext, !!apiContext.multiZoneDraftMode)\n    )\n    // Checking if preview mode is enabled\n    setLazyProp({ req: apiReq }, 'preview', () =>\n      apiReq.previewData !== false ? true : undefined\n    )\n    // Set draftMode to the same value as preview\n    setLazyProp({ req: apiReq }, 'draftMode', () => apiReq.preview)\n\n    // Parsing of body\n    if (bodyParser && !apiReq.body) {\n      apiReq.body = await parseBody(\n        apiReq,\n        config.api && config.api.bodyParser && config.api.bodyParser.sizeLimit\n          ? config.api.bodyParser.sizeLimit\n          : '1mb'\n      )\n    }\n\n    let contentLength = 0\n    const maxContentLength = getMaxContentLength(responseLimit)\n    const writeData = apiRes.write\n    const endResponse = apiRes.end\n    apiRes.write = (...args: any[2]) => {\n      contentLength += Buffer.byteLength(args[0] || '')\n      return writeData.apply(apiRes, args)\n    }\n    apiRes.end = (...args: any[2]) => {\n      if (args.length && typeof args[0] !== 'function') {\n        contentLength += Buffer.byteLength(args[0] || '')\n      }\n\n      if (responseLimit && contentLength >= maxContentLength) {\n        console.warn(\n          `API response for ${req.url} exceeds ${bytes.format(\n            maxContentLength\n          )}. API Routes are meant to respond quickly. https://nextjs.org/docs/messages/api-routes-response-size-limit`\n        )\n      }\n\n      return endResponse.apply(apiRes, args)\n    }\n    apiRes.status = (statusCode) => sendStatusCode(apiRes, statusCode)\n    apiRes.send = (data) => sendData(apiReq, apiRes, data)\n    apiRes.json = (data) => sendJson(apiRes, data)\n    apiRes.redirect = (statusOrUrl: number | string, url?: string) =>\n      redirect(apiRes, statusOrUrl, url)\n    apiRes.setDraftMode = (options = { enable: true }) =>\n      setDraftMode(apiRes, Object.assign({}, apiContext, options))\n    apiRes.setPreviewData = (data, options = {}) =>\n      setPreviewData(apiRes, data, Object.assign({}, apiContext, options))\n    apiRes.clearPreviewData = (options = {}) =>\n      clearPreviewData(apiRes, options)\n    apiRes.revalidate = (\n      urlPath: string,\n      opts?: {\n        unstable_onlyGenerated?: boolean\n      }\n    ) => revalidate(urlPath, opts || {}, req, apiContext)\n\n    const resolver = interopDefault(resolverModule)\n    let wasPiped = false\n\n    if (process.env.NODE_ENV !== 'production') {\n      // listen for pipe event and don't show resolve warning\n      res.once('pipe', () => (wasPiped = true))\n    }\n\n    const apiRouteResult = await resolver(req, res)\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof apiRouteResult !== 'undefined') {\n        if (apiRouteResult instanceof Response) {\n          throw new Error(\n            'API route returned a Response object in the Node.js runtime, this is not supported. Please use `runtime: \"edge\"` instead: https://nextjs.org/docs/api-routes/edge-api-routes'\n          )\n        }\n        console.warn(\n          `API handler should not return a value, received ${typeof apiRouteResult}.`\n        )\n      }\n\n      if (!externalResolver && !isResSent(res) && !wasPiped) {\n        console.warn(\n          `API resolved without sending a response for ${req.url}, this may result in stalled requests.`\n        )\n      }\n    }\n  } catch (err) {\n    onError?.(err, req, {\n      routerKind: 'Pages Router',\n      routePath: page || '',\n      routeType: 'route',\n      revalidateReason: undefined,\n    })\n\n    if (err instanceof ApiError) {\n      sendError(apiRes, err.statusCode, err.message)\n    } else {\n      if (dev) {\n        if (isError(err)) {\n          err.page = page\n        }\n        throw err\n      }\n\n      console.error(err)\n      if (propagateError) {\n        throw err\n      }\n      sendError(apiRes, 500, 'Internal Server Error')\n    }\n  }\n}\n"], "names": ["apiResolver", "getMaxContentLength", "responseLimit", "bytes", "parse", "RESPONSE_LIMIT_DEFAULT", "sendData", "req", "res", "body", "undefined", "end", "statusCode", "removeHeader", "process", "env", "NODE_ENV", "console", "warn", "url", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "Stream", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "isJSONLike", "includes", "stringifiedBody", "JSON", "stringify", "etag", "generateETag", "sendEtagResponse", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "byteLength", "<PERSON><PERSON><PERSON>", "jsonBody", "send", "isValidData", "str", "setDraftMode", "options", "previewModeId", "Error", "expires", "enable", "Date", "serialize", "require", "previous", "Array", "isArray", "COOKIE_NAME_PRERENDER_BYPASS", "httpOnly", "sameSite", "secure", "path", "setPreviewData", "data", "previewModeEncryptionKey", "previewModeSigningKey", "jsonwebtoken", "encryptWithSecret", "payload", "sign", "from", "algorithm", "maxAge", "expiresIn", "COOKIE_NAME_PRERENDER_DATA", "revalidate", "url<PERSON><PERSON>", "opts", "context", "startsWith", "revalidateHeaders", "PRERENDER_REVALIDATE_HEADER", "unstable_onlyGenerated", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "allowedRevalidateHeaderKeys", "trustHostHeader", "dev", "push", "key", "Object", "keys", "headers", "fetch", "host", "method", "cacheHeader", "get", "toUpperCase", "status", "err", "isError", "message", "query", "resolverModule", "apiContext", "propagateError", "page", "onError", "apiReq", "apiRes", "config", "<PERSON><PERSON><PERSON><PERSON>", "api", "externalResolver", "setLazyProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tryGetPreviewData", "multiZoneDraftMode", "previewData", "preview", "parseBody", "sizeLimit", "contentLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writeData", "write", "endResponse", "args", "apply", "format", "sendStatusCode", "json", "redirect", "statusOrUrl", "assign", "clearPreviewData", "resolver", "interopDefault", "wasPiped", "once", "apiRouteResult", "Response", "isResSent", "routerKind", "routePath", "routeType", "revalidateReason", "ApiError", "sendError", "error"], "mappings": ";;;;+BAuUsBA;;;eAAAA;;;8DAhUJ;sBACW;6BACI;wBACV;gEACH;uBACM;gCACK;uBAWxB;iCACyB;2BAIzB;mCAC2B;2BACR;;;;;;AAiB1B,SAASC,oBAAoBC,aAA6B;IACxD,IAAIA,iBAAiB,OAAOA,kBAAkB,WAAW;QACvD,OAAOC,cAAK,CAACC,KAAK,CAACF;IACrB;IACA,OAAOG,6BAAsB;AAC/B;AAEA;;;;;CAKC,GACD,SAASC,SAASC,GAAmB,EAAEC,GAAoB,EAAEC,IAAS;IACpE,IAAIA,SAAS,QAAQA,SAASC,WAAW;QACvCF,IAAIG,GAAG;QACP;IACF;IAEA,gCAAgC;IAChC,IAAIH,IAAII,UAAU,KAAK,OAAOJ,IAAII,UAAU,KAAK,KAAK;QACpDJ,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QAEjB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBP,MAAM;YAClDQ,QAAQC,IAAI,CACV,CAAC,yDAAyD,EAAEX,IAAIY,GAAG,CAAC,6CAA6C,CAAC,GAChH,CAAC,2EAA2E,CAAC;QAEnF;QACAX,IAAIG,GAAG;QACP;IACF;IAEA,MAAMS,cAAcZ,IAAIa,SAAS,CAAC;IAElC,IAAIZ,gBAAgBa,cAAM,EAAE;QAC1B,IAAI,CAACF,aAAa;YAChBZ,IAAIe,SAAS,CAAC,gBAAgB;QAChC;QACAd,KAAKe,IAAI,CAAChB;QACV;IACF;IAEA,MAAMiB,aAAa;QAAC;QAAU;QAAU;KAAU,CAACC,QAAQ,CAAC,OAAOjB;IACnE,MAAMkB,kBAAkBF,aAAaG,KAAKC,SAAS,CAACpB,QAAQA;IAC5D,MAAMqB,OAAOC,IAAAA,kBAAY,EAACJ;IAC1B,IAAIK,IAAAA,6BAAgB,EAACzB,KAAKC,KAAKsB,OAAO;QACpC;IACF;IAEA,IAAIG,OAAOC,QAAQ,CAACzB,OAAO;QACzB,IAAI,CAACW,aAAa;YAChBZ,IAAIe,SAAS,CAAC,gBAAgB;QAChC;QACAf,IAAIe,SAAS,CAAC,kBAAkBd,KAAK0B,MAAM;QAC3C3B,IAAIG,GAAG,CAACF;QACR;IACF;IAEA,IAAIgB,YAAY;QACdjB,IAAIe,SAAS,CAAC,gBAAgB;IAChC;IAEAf,IAAIe,SAAS,CAAC,kBAAkBU,OAAOG,UAAU,CAACT;IAClDnB,IAAIG,GAAG,CAACgB;AACV;AAEA;;;;CAIC,GACD,SAASU,SAAS7B,GAAoB,EAAE8B,QAAa;IACnD,iCAAiC;IACjC9B,IAAIe,SAAS,CAAC,gBAAgB;IAE9B,6BAA6B;IAC7Bf,IAAI+B,IAAI,CAACX,KAAKC,SAAS,CAACS;AAC1B;AAEA,SAASE,YAAYC,GAAQ;IAC3B,OAAO,OAAOA,QAAQ,YAAYA,IAAIN,MAAM,IAAI;AAClD;AAEA,SAASO,aACPlC,GAAuB,EACvBmC,OAGC;IAED,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,qBAA6C,CAA7C,IAAIC,MAAM,qCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4C;IACpD;IACA,MAAMC,UAAUH,QAAQI,MAAM,GAAGrC,YAAY,IAAIsC,KAAK;IACtD,2DAA2D;IAC3D,oDAAoD;IACpD,wEAAwE;IACxE,MAAM,EAAEC,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW3C,IAAIa,SAAS,CAAC;IAC/Bb,IAAIe,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO4B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACZA,WACA,EAAE;QACRF,UAAUK,mCAA4B,EAAEX,QAAQC,aAAa,EAAE;YAC7DW,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACNZ;QACF;KACD;IACD,OAAOtC;AACT;AAEA,SAASmD,eACPnD,GAAuB,EACvBoD,IAAqB,EACrBjB,OAGqB;IAErB,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,qBAA6C,CAA7C,IAAIC,MAAM,qCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4C;IACpD;IACA,IAAI,CAACL,YAAYG,QAAQkB,wBAAwB,GAAG;QAClD,MAAM,qBAAwD,CAAxD,IAAIhB,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IACA,IAAI,CAACL,YAAYG,QAAQmB,qBAAqB,GAAG;QAC/C,MAAM,qBAAqD,CAArD,IAAIjB,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAMkB,eACJb,QAAQ;IACV,MAAM,EAAEc,iBAAiB,EAAE,GACzBd,QAAQ;IACV,MAAMe,UAAUF,aAAaG,IAAI,CAC/B;QACEN,MAAMI,kBACJ/B,OAAOkC,IAAI,CAACxB,QAAQkB,wBAAwB,GAC5CjC,KAAKC,SAAS,CAAC+B;IAEnB,GACAjB,QAAQmB,qBAAqB,EAC7B;QACEM,WAAW;QACX,GAAIzB,QAAQ0B,MAAM,KAAK3D,YACnB;YAAE4D,WAAW3B,QAAQ0B,MAAM;QAAC,IAC5B3D,SAAS;IACf;IAGF,qEAAqE;IACrE,+CAA+C;IAC/C,IAAIuD,QAAQ9B,MAAM,GAAG,MAAM;QACzB,MAAM,qBAEL,CAFK,IAAIU,MACR,CAAC,0GAA0G,CAAC,GADxG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM,EAAEI,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW3C,IAAIa,SAAS,CAAC;IAC/Bb,IAAIe,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO4B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACZA,WACA,EAAE;QACRF,UAAUK,mCAA4B,EAAEX,QAAQC,aAAa,EAAE;YAC7DW,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACN,GAAIf,QAAQ0B,MAAM,KAAK3D,YAClB;gBAAE2D,QAAQ1B,QAAQ0B,MAAM;YAAC,IAC1B3D,SAAS;YACb,GAAIiC,QAAQe,IAAI,KAAKhD,YAChB;gBAAEgD,MAAMf,QAAQe,IAAI;YAAC,IACtBhD,SAAS;QACf;QACAuC,UAAUsB,iCAA0B,EAAEN,SAAS;YAC7CV,UAAU;YACVC,UAAU1C,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DyC,QAAQ3C,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjC0C,MAAM;YACN,GAAIf,QAAQ0B,MAAM,KAAK3D,YAClB;gBAAE2D,QAAQ1B,QAAQ0B,MAAM;YAAC,IAC1B3D,SAAS;YACb,GAAIiC,QAAQe,IAAI,KAAKhD,YAChB;gBAAEgD,MAAMf,QAAQe,IAAI;YAAC,IACtBhD,SAAS;QACf;KACD;IACD,OAAOF;AACT;AAEA,eAAegE,WACbC,OAAe,EACfC,IAEC,EACDnE,GAAoB,EACpBoE,OAAmB;IAEnB,IAAI,OAAOF,YAAY,YAAY,CAACA,QAAQG,UAAU,CAAC,MAAM;QAC3D,MAAM,qBAEL,CAFK,IAAI/B,MACR,CAAC,qFAAqF,EAAE4B,SAAS,GAD7F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMI,oBAAiC;QACrC,CAACC,sCAA2B,CAAC,EAAEH,QAAQ/B,aAAa;QACpD,GAAI8B,KAAKK,sBAAsB,GAC3B;YACE,CAACC,qDAA0C,CAAC,EAAE;QAChD,IACA,CAAC,CAAC;IACR;IACA,MAAMC,8BAA8B;WAC9BN,QAAQM,2BAA2B,IAAI,EAAE;KAC9C;IAED,IAAIN,QAAQO,eAAe,IAAIP,QAAQQ,GAAG,EAAE;QAC1CF,4BAA4BG,IAAI,CAAC;IACnC;IAEA,IAAIT,QAAQO,eAAe,EAAE;QAC3BD,4BAA4BG,IAAI,CAAC;IACnC;IAEA,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAAChF,IAAIiF,OAAO,EAAG;QAC1C,IAAIP,4BAA4BvD,QAAQ,CAAC2D,MAAM;YAC7CR,iBAAiB,CAACQ,IAAI,GAAG9E,IAAIiF,OAAO,CAACH,IAAI;QAC3C;IACF;IAEA,IAAI;QACF,IAAIV,QAAQO,eAAe,EAAE;YAC3B,MAAM1E,MAAM,MAAMiF,MAAM,CAAC,QAAQ,EAAElF,IAAIiF,OAAO,CAACE,IAAI,GAAGjB,SAAS,EAAE;gBAC/DkB,QAAQ;gBACRH,SAASX;YACX;YACA,gEAAgE;YAChE,qEAAqE;YACrE,gEAAgE;YAChE,MAAMe,cACJpF,IAAIgF,OAAO,CAACK,GAAG,CAAC,qBAAqBrF,IAAIgF,OAAO,CAACK,GAAG,CAAC;YAEvD,IACED,CAAAA,+BAAAA,YAAaE,WAAW,QAAO,iBAC/BtF,IAAIuF,MAAM,KAAK,OACf,CAAEvF,CAAAA,IAAIuF,MAAM,KAAK,OAAOrB,KAAKK,sBAAsB,AAAD,GAClD;gBACA,MAAM,qBAA2C,CAA3C,IAAIlC,MAAM,CAAC,iBAAiB,EAAErC,IAAIuF,MAAM,EAAE,GAA1C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0C;YAClD;QACF,OAAO,IAAIpB,QAAQH,UAAU,EAAE;YAC7B,MAAMG,QAAQH,UAAU,CAAC;gBACvBC;gBACAI;gBACAH;YACF;QACF,OAAO;YACL,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,sEAAsE,CAAC,GADpE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF,EAAE,OAAOmD,KAAc;QACrB,MAAM,qBAEL,CAFK,IAAInD,MACR,CAAC,qBAAqB,EAAE4B,QAAQ,EAAE,EAAEwB,IAAAA,gBAAO,EAACD,OAAOA,IAAIE,OAAO,GAAGF,KAAK,GADlE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEO,eAAehG,YACpBO,GAAoB,EACpBC,GAAmB,EACnB2F,KAAU,EACVC,cAAmB,EACnBC,UAAsB,EACtBC,cAAuB,EACvBnB,GAAa,EACboB,IAAa,EACbC,OAA6C;IAE7C,MAAMC,SAASlG;IACf,MAAMmG,SAASlG;IAEf,IAAI;YAOiBmG,aACGA,cACGA;QARzB,IAAI,CAACP,gBAAgB;YACnB5F,IAAII,UAAU,GAAG;YACjBJ,IAAIG,GAAG,CAAC;YACR;QACF;QACA,MAAMgG,SAAqBP,eAAeO,MAAM,IAAI,CAAC;QACrD,MAAMC,aAAaD,EAAAA,cAAAA,OAAOE,GAAG,qBAAVF,YAAYC,UAAU,MAAK;QAC9C,MAAM1G,gBAAgByG,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAYzG,aAAa,KAAI;QACnD,MAAM4G,mBAAmBH,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAYG,gBAAgB,KAAI;QAEzD,qBAAqB;QACrBC,IAAAA,kBAAW,EAAC;YAAExG,KAAKkG;QAAO,GAAG,WAAWO,IAAAA,gCAAe,EAACzG,IAAIiF,OAAO;QACnE,uBAAuB;QACvBiB,OAAON,KAAK,GAAGA;QACf,uBAAuB;QACvBY,IAAAA,kBAAW,EAAC;YAAExG,KAAKkG;QAAO,GAAG,eAAe,IAC1CQ,IAAAA,oCAAiB,EAAC1G,KAAKC,KAAK6F,YAAY,CAAC,CAACA,WAAWa,kBAAkB;QAEzE,sCAAsC;QACtCH,IAAAA,kBAAW,EAAC;YAAExG,KAAKkG;QAAO,GAAG,WAAW,IACtCA,OAAOU,WAAW,KAAK,QAAQ,OAAOzG;QAExC,6CAA6C;QAC7CqG,IAAAA,kBAAW,EAAC;YAAExG,KAAKkG;QAAO,GAAG,aAAa,IAAMA,OAAOW,OAAO;QAE9D,kBAAkB;QAClB,IAAIR,cAAc,CAACH,OAAOhG,IAAI,EAAE;YAC9BgG,OAAOhG,IAAI,GAAG,MAAM4G,IAAAA,oBAAS,EAC3BZ,QACAE,OAAOE,GAAG,IAAIF,OAAOE,GAAG,CAACD,UAAU,IAAID,OAAOE,GAAG,CAACD,UAAU,CAACU,SAAS,GAClEX,OAAOE,GAAG,CAACD,UAAU,CAACU,SAAS,GAC/B;QAER;QAEA,IAAIC,gBAAgB;QACpB,MAAMC,mBAAmBvH,oBAAoBC;QAC7C,MAAMuH,YAAYf,OAAOgB,KAAK;QAC9B,MAAMC,cAAcjB,OAAO/F,GAAG;QAC9B+F,OAAOgB,KAAK,GAAG,CAAC,GAAGE;YACjBL,iBAAiBtF,OAAOG,UAAU,CAACwF,IAAI,CAAC,EAAE,IAAI;YAC9C,OAAOH,UAAUI,KAAK,CAACnB,QAAQkB;QACjC;QACAlB,OAAO/F,GAAG,GAAG,CAAC,GAAGiH;YACf,IAAIA,KAAKzF,MAAM,IAAI,OAAOyF,IAAI,CAAC,EAAE,KAAK,YAAY;gBAChDL,iBAAiBtF,OAAOG,UAAU,CAACwF,IAAI,CAAC,EAAE,IAAI;YAChD;YAEA,IAAI1H,iBAAiBqH,iBAAiBC,kBAAkB;gBACtDvG,QAAQC,IAAI,CACV,CAAC,iBAAiB,EAAEX,IAAIY,GAAG,CAAC,SAAS,EAAEhB,cAAK,CAAC2H,MAAM,CACjDN,kBACA,0GAA0G,CAAC;YAEjH;YAEA,OAAOG,YAAYE,KAAK,CAACnB,QAAQkB;QACnC;QACAlB,OAAOX,MAAM,GAAG,CAACnF,aAAemH,IAAAA,qBAAc,EAACrB,QAAQ9F;QACvD8F,OAAOnE,IAAI,GAAG,CAACqB,OAAStD,SAASmG,QAAQC,QAAQ9C;QACjD8C,OAAOsB,IAAI,GAAG,CAACpE,OAASvB,SAASqE,QAAQ9C;QACzC8C,OAAOuB,QAAQ,GAAG,CAACC,aAA8B/G,MAC/C8G,IAAAA,eAAQ,EAACvB,QAAQwB,aAAa/G;QAChCuF,OAAOhE,YAAY,GAAG,CAACC,UAAU;YAAEI,QAAQ;QAAK,CAAC,GAC/CL,aAAagE,QAAQpB,OAAO6C,MAAM,CAAC,CAAC,GAAG9B,YAAY1D;QACrD+D,OAAO/C,cAAc,GAAG,CAACC,MAAMjB,UAAU,CAAC,CAAC,GACzCgB,eAAe+C,QAAQ9C,MAAM0B,OAAO6C,MAAM,CAAC,CAAC,GAAG9B,YAAY1D;QAC7D+D,OAAO0B,gBAAgB,GAAG,CAACzF,UAAU,CAAC,CAAC,GACrCyF,IAAAA,uBAAgB,EAAC1B,QAAQ/D;QAC3B+D,OAAOlC,UAAU,GAAG,CAClBC,SACAC,OAGGF,WAAWC,SAASC,QAAQ,CAAC,GAAGnE,KAAK8F;QAE1C,MAAMgC,WAAWC,IAAAA,8BAAc,EAAClC;QAChC,IAAImC,WAAW;QAEf,IAAIzH,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,uDAAuD;YACvDR,IAAIgI,IAAI,CAAC,QAAQ,IAAOD,WAAW;QACrC;QAEA,MAAME,iBAAiB,MAAMJ,SAAS9H,KAAKC;QAE3C,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAOyH,mBAAmB,aAAa;gBACzC,IAAIA,0BAA0BC,UAAU;oBACtC,MAAM,qBAEL,CAFK,IAAI7F,MACR,iLADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA5B,QAAQC,IAAI,CACV,CAAC,gDAAgD,EAAE,OAAOuH,eAAe,CAAC,CAAC;YAE/E;YAEA,IAAI,CAAC3B,oBAAoB,CAAC6B,IAAAA,gBAAS,EAACnI,QAAQ,CAAC+H,UAAU;gBACrDtH,QAAQC,IAAI,CACV,CAAC,4CAA4C,EAAEX,IAAIY,GAAG,CAAC,sCAAsC,CAAC;YAElG;QACF;IACF,EAAE,OAAO6E,KAAK;QACZQ,2BAAAA,QAAUR,KAAKzF,KAAK;YAClBqI,YAAY;YACZC,WAAWtC,QAAQ;YACnBuC,WAAW;YACXC,kBAAkBrI;QACpB;QAEA,IAAIsF,eAAegD,eAAQ,EAAE;YAC3BC,IAAAA,gBAAS,EAACvC,QAAQV,IAAIpF,UAAU,EAAEoF,IAAIE,OAAO;QAC/C,OAAO;YACL,IAAIf,KAAK;gBACP,IAAIc,IAAAA,gBAAO,EAACD,MAAM;oBAChBA,IAAIO,IAAI,GAAGA;gBACb;gBACA,MAAMP;YACR;YAEA/E,QAAQiI,KAAK,CAAClD;YACd,IAAIM,gBAAgB;gBAClB,MAAMN;YACR;YACAiD,IAAAA,gBAAS,EAACvC,QAAQ,KAAK;QACzB;IACF;AACF"}