{"version": 3, "sources": ["../../../../src/server/lib/router-utils/resolve-routes.ts"], "sourcesContent": ["import type { FsOutput } from './filesystem'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { RenderServer, initialize } from '../router-server'\nimport type { PatchMatcher } from '../../../shared/lib/router/utils/path-match'\nimport type { Redirect } from '../../../types'\nimport type { Header, Rewrite } from '../../../lib/load-custom-routes'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport type { NextUrlWithParsedQuery } from '../../request-meta'\n\nimport url from 'url'\nimport path from 'node:path'\nimport setupDebug from 'next/dist/compiled/debug'\nimport { getCloneableBody } from '../../body-streams'\nimport { filterReqHeaders, ipcForbiddenHeaders } from '../server-ipc/utils'\nimport { stringifyQuery } from '../../server-route-utils'\nimport { formatHostname } from '../format-hostname'\nimport { toNodeOutgoingHttpHeaders } from '../../web/utils'\nimport { isAbortError } from '../../pipe-readable'\nimport { getHostname } from '../../../shared/lib/get-hostname'\nimport { getRedirectStatus } from '../../../lib/redirect-status'\nimport { normalizeRepeatedSlashes } from '../../../shared/lib/utils'\nimport { getRelativeURL } from '../../../shared/lib/router/utils/relativize-url'\nimport { addPathPrefix } from '../../../shared/lib/router/utils/add-path-prefix'\nimport { pathHasPrefix } from '../../../shared/lib/router/utils/path-has-prefix'\nimport { detectDomainLocale } from '../../../shared/lib/i18n/detect-domain-locale'\nimport { normalizeLocalePath } from '../../../shared/lib/i18n/normalize-locale-path'\nimport { removePathPrefix } from '../../../shared/lib/router/utils/remove-path-prefix'\nimport { NextDataPathnameNormalizer } from '../../normalizers/request/next-data'\nimport { BasePathPathnameNormalizer } from '../../normalizers/request/base-path'\n\nimport { addRequestMeta } from '../../request-meta'\nimport {\n  compileNonPath,\n  matchHas,\n  parseDestination,\n  prepareDestination,\n} from '../../../shared/lib/router/utils/prepare-destination'\nimport type { TLSSocket } from 'tls'\nimport {\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  RSC_HEADER,\n} from '../../../client/components/app-router-headers'\nimport { getSelectedParams } from '../../../client/components/router-reducer/compute-changed-path'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport { parseAndValidateFlightRouterState } from '../../app-render/parse-and-validate-flight-router-state'\n\nconst debug = setupDebug('next:router-server:resolve-routes')\n\nexport function getResolveRoutes(\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >,\n  config: NextConfigComplete,\n  opts: Parameters<typeof initialize>[0],\n  renderServer: RenderServer,\n  renderServerOpts: Parameters<RenderServer['initialize']>[0],\n  ensureMiddleware?: (url?: string) => Promise<void>\n) {\n  type Route = {\n    /**\n     * The path matcher to check if this route applies to this request.\n     */\n    match: PatchMatcher\n    check?: boolean\n    name?: string\n  } & Partial<Header> &\n    Partial<Redirect>\n\n  const routes: Route[] = [\n    // _next/data with middleware handling\n    { match: () => ({}), name: 'middleware_next_data' },\n\n    ...(opts.minimalMode ? [] : fsChecker.headers),\n    ...(opts.minimalMode ? [] : fsChecker.redirects),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'middleware' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.beforeFiles),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'before_files_end' },\n\n    // we check exact matches on fs before continuing to\n    // after files rewrites\n    { match: () => ({}), name: 'check_fs' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.afterFiles),\n\n    // we always do the check: true handling before continuing to\n    // fallback rewrites\n    {\n      check: true,\n      match: () => ({}),\n      name: 'after files check: true',\n    },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.fallback),\n  ]\n\n  async function resolveRoutes({\n    req,\n    res,\n    isUpgradeReq,\n    invokedOutputs,\n  }: {\n    req: IncomingMessage\n    res: ServerResponse\n    isUpgradeReq: boolean\n    signal: AbortSignal\n    invokedOutputs?: Set<string>\n  }): Promise<{\n    finished: boolean\n    statusCode?: number\n    bodyStream?: ReadableStream | null\n    resHeaders: Record<string, string | string[]>\n    parsedUrl: NextUrlWithParsedQuery\n    matchedOutput?: FsOutput | null\n  }> {\n    let finished = false\n    let resHeaders: Record<string, string | string[]> = {}\n    let matchedOutput: FsOutput | null = null\n    let parsedUrl = url.parse(req.url || '', true) as NextUrlWithParsedQuery\n    let didRewrite = false\n\n    const urlParts = (req.url || '').split('?', 1)\n    const urlNoQuery = urlParts[0]\n\n    // this normalizes repeated slashes in the path e.g. hello//world ->\n    // hello/world or backslashes to forward slashes, this does not\n    // handle trailing slash as that is handled the same as a next.config.js\n    // redirect\n    if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n      parsedUrl = url.parse(normalizeRepeatedSlashes(req.url!), true)\n      return {\n        parsedUrl,\n        resHeaders,\n        finished: true,\n        statusCode: 308,\n      }\n    }\n    // TODO: inherit this from higher up\n    const protocol =\n      (req?.socket as TLSSocket)?.encrypted ||\n      req.headers['x-forwarded-proto']?.includes('https')\n        ? 'https'\n        : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl = (config.experimental as any).trustHostHeader\n      ? `https://${req.headers.host || 'localhost'}${req.url}`\n      : opts.port\n        ? `${protocol}://${formatHostname(opts.hostname || 'localhost')}:${\n            opts.port\n          }${req.url}`\n        : req.url || ''\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req))\n    }\n\n    const maybeAddTrailingSlash = (pathname: string) => {\n      if (\n        config.trailingSlash &&\n        !config.skipMiddlewareUrlNormalize &&\n        !pathname.endsWith('/')\n      ) {\n        return `${pathname}/`\n      }\n      return pathname\n    }\n\n    let domainLocale: ReturnType<typeof detectDomainLocale> | undefined\n    let defaultLocale: string | undefined\n    let initialLocaleResult:\n      | ReturnType<typeof normalizeLocalePath>\n      | undefined = undefined\n\n    if (config.i18n) {\n      const hadTrailingSlash = parsedUrl.pathname?.endsWith('/')\n      const hadBasePath = pathHasPrefix(\n        parsedUrl.pathname || '',\n        config.basePath\n      )\n      initialLocaleResult = normalizeLocalePath(\n        removePathPrefix(parsedUrl.pathname || '/', config.basePath),\n        config.i18n.locales\n      )\n\n      domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname(parsedUrl, req.headers)\n      )\n      defaultLocale = domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n      addRequestMeta(\n        req,\n        'locale',\n        initialLocaleResult.detectedLocale || defaultLocale\n      )\n\n      // ensure locale is present for resolving routes\n      if (\n        !initialLocaleResult.detectedLocale &&\n        !initialLocaleResult.pathname.startsWith('/_next/')\n      ) {\n        parsedUrl.pathname = addPathPrefix(\n          initialLocaleResult.pathname === '/'\n            ? `/${defaultLocale}`\n            : addPathPrefix(\n                initialLocaleResult.pathname || '',\n                `/${defaultLocale}`\n              ),\n          hadBasePath ? config.basePath : ''\n        )\n\n        if (hadTrailingSlash) {\n          parsedUrl.pathname = maybeAddTrailingSlash(parsedUrl.pathname)\n        }\n      }\n    }\n\n    const checkLocaleApi = (pathname: string) => {\n      if (\n        config.i18n &&\n        pathname === urlNoQuery &&\n        initialLocaleResult?.detectedLocale &&\n        pathHasPrefix(initialLocaleResult.pathname, '/api')\n      ) {\n        return true\n      }\n    }\n\n    async function checkTrue() {\n      const pathname = parsedUrl.pathname || ''\n\n      if (checkLocaleApi(pathname)) {\n        return\n      }\n      if (!invokedOutputs?.has(pathname)) {\n        const output = await fsChecker.getItem(pathname)\n\n        if (output) {\n          if (\n            config.useFileSystemPublicRoutes ||\n            didRewrite ||\n            (output.type !== 'appFile' && output.type !== 'pageFile')\n          ) {\n            return output\n          }\n        }\n      }\n      const dynamicRoutes = fsChecker.getDynamicRoutes()\n      let curPathname = parsedUrl.pathname\n\n      if (config.basePath) {\n        if (!pathHasPrefix(curPathname || '', config.basePath)) {\n          return\n        }\n        curPathname = curPathname?.substring(config.basePath.length) || '/'\n      }\n      const localeResult = fsChecker.handleLocale(curPathname || '')\n\n      for (const route of dynamicRoutes) {\n        // when resolving fallback: false the\n        // render worker may return a no-fallback response\n        // which signals we need to continue resolving.\n        // TODO: optimize this to collect static paths\n        // to use at the routing layer\n        if (invokedOutputs?.has(route.page)) {\n          continue\n        }\n        const params = route.match(localeResult.pathname)\n\n        if (params) {\n          const pageOutput = await fsChecker.getItem(\n            addPathPrefix(route.page, config.basePath || '')\n          )\n\n          // i18n locales aren't matched for app dir\n          if (\n            pageOutput?.type === 'appFile' &&\n            initialLocaleResult?.detectedLocale\n          ) {\n            continue\n          }\n\n          if (pageOutput && curPathname?.startsWith('/_next/data')) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n\n          if (config.useFileSystemPublicRoutes || didRewrite) {\n            return pageOutput\n          }\n        }\n      }\n    }\n\n    const normalizers = {\n      basePath:\n        config.basePath && config.basePath !== '/'\n          ? new BasePathPathnameNormalizer(config.basePath)\n          : undefined,\n      data: new NextDataPathnameNormalizer(fsChecker.buildId),\n    }\n\n    async function handleRoute(\n      route: (typeof routes)[0]\n    ): Promise<UnwrapPromise<ReturnType<typeof resolveRoutes>> | void> {\n      let curPathname = parsedUrl.pathname || '/'\n\n      if (config.i18n && route.internal) {\n        const hadTrailingSlash = curPathname.endsWith('/')\n\n        if (config.basePath) {\n          curPathname = removePathPrefix(curPathname, config.basePath)\n        }\n        const hadBasePath = curPathname !== parsedUrl.pathname\n\n        const localeResult = normalizeLocalePath(\n          curPathname,\n          config.i18n.locales\n        )\n        const isDefaultLocale = localeResult.detectedLocale === defaultLocale\n\n        if (isDefaultLocale) {\n          curPathname =\n            localeResult.pathname === '/' && hadBasePath\n              ? config.basePath\n              : addPathPrefix(\n                  localeResult.pathname,\n                  hadBasePath ? config.basePath : ''\n                )\n        } else if (hadBasePath) {\n          curPathname =\n            curPathname === '/'\n              ? config.basePath\n              : addPathPrefix(curPathname, config.basePath)\n        }\n\n        if ((isDefaultLocale || hadBasePath) && hadTrailingSlash) {\n          curPathname = maybeAddTrailingSlash(curPathname)\n        }\n      }\n      let params = route.match(curPathname)\n\n      if ((route.has || route.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          route.has,\n          route.missing\n        )\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        if (\n          fsChecker.exportPathMapRoutes &&\n          route.name === 'before_files_end'\n        ) {\n          for (const exportPathMapRoute of fsChecker.exportPathMapRoutes) {\n            const result = await handleRoute(exportPathMapRoute)\n\n            if (result) {\n              return result\n            }\n          }\n        }\n\n        if (route.name === 'middleware_next_data' && parsedUrl.pathname) {\n          if (fsChecker.getMiddlewareMatchers()?.length) {\n            let normalized = parsedUrl.pathname\n\n            // Remove the base path if it exists.\n            const hadBasePath = normalizers.basePath?.match(parsedUrl.pathname)\n            if (hadBasePath && normalizers.basePath) {\n              normalized = normalizers.basePath.normalize(normalized, true)\n            }\n\n            let updated = false\n            if (normalizers.data.match(normalized)) {\n              updated = true\n              addRequestMeta(req, 'isNextDataReq', true)\n              normalized = normalizers.data.normalize(normalized, true)\n            }\n\n            if (config.i18n) {\n              const curLocaleResult = normalizeLocalePath(\n                normalized,\n                config.i18n.locales\n              )\n\n              if (curLocaleResult.detectedLocale) {\n                addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n              }\n            }\n\n            // If we updated the pathname, and it had a base path, re-add the\n            // base path.\n            if (updated) {\n              if (hadBasePath) {\n                normalized = path.posix.join(config.basePath, normalized)\n              }\n\n              // Re-add the trailing slash (if required).\n              normalized = maybeAddTrailingSlash(normalized)\n\n              parsedUrl.pathname = normalized\n            }\n          }\n        }\n\n        if (route.name === 'check_fs') {\n          const pathname = parsedUrl.pathname || ''\n\n          if (invokedOutputs?.has(pathname) || checkLocaleApi(pathname)) {\n            return\n          }\n          const output = await fsChecker.getItem(pathname)\n\n          if (\n            output &&\n            !(\n              config.i18n &&\n              initialLocaleResult?.detectedLocale &&\n              pathHasPrefix(pathname, '/api')\n            )\n          ) {\n            if (\n              config.useFileSystemPublicRoutes ||\n              didRewrite ||\n              (output.type !== 'appFile' && output.type !== 'pageFile')\n            ) {\n              matchedOutput = output\n\n              if (output.locale) {\n                addRequestMeta(req, 'locale', output.locale)\n              }\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                matchedOutput,\n              }\n            }\n          }\n        }\n\n        if (!opts.minimalMode && route.name === 'middleware') {\n          const match = fsChecker.getMiddlewareMatchers()\n          let maybeDecodedPathname = parsedUrl.pathname || '/'\n\n          try {\n            maybeDecodedPathname = decodeURIComponent(maybeDecodedPathname)\n          } catch {\n            /* non-fatal we can't decode so can't match it */\n          }\n\n          if (\n            // @ts-expect-error BaseNextRequest stuff\n            match?.(parsedUrl.pathname, req, parsedUrl.query) ||\n            match?.(\n              maybeDecodedPathname,\n              // @ts-expect-error BaseNextRequest stuff\n              req,\n              parsedUrl.query\n            )\n          ) {\n            if (ensureMiddleware) {\n              await ensureMiddleware(req.url)\n            }\n\n            const serverResult =\n              await renderServer?.initialize(renderServerOpts)\n\n            if (!serverResult) {\n              throw new Error(`Failed to initialize render server \"middleware\"`)\n            }\n\n            addRequestMeta(req, 'invokePath', '')\n            addRequestMeta(req, 'invokeOutput', '')\n            addRequestMeta(req, 'invokeQuery', {})\n            addRequestMeta(req, 'middlewareInvoke', true)\n            debug('invoking middleware', req.url, req.headers)\n\n            let middlewareRes: Response | undefined = undefined\n            let bodyStream: ReadableStream | undefined = undefined\n            try {\n              try {\n                await serverResult.requestHandler(req, res, parsedUrl)\n              } catch (err: any) {\n                if (!('result' in err) || !('response' in err.result)) {\n                  throw err\n                }\n                middlewareRes = err.result.response as Response\n                res.statusCode = middlewareRes.status\n\n                if (middlewareRes.body) {\n                  bodyStream = middlewareRes.body\n                } else if (middlewareRes.status) {\n                  bodyStream = new ReadableStream({\n                    start(controller) {\n                      controller.enqueue('')\n                      controller.close()\n                    },\n                  })\n                }\n              }\n            } catch (e) {\n              // If the client aborts before we can receive a response object\n              // (when the headers are flushed), then we can early exit without\n              // further processing.\n              if (isAbortError(e)) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n              throw e\n            }\n\n            if (res.closed || res.finished || !middlewareRes) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n              }\n            }\n\n            const middlewareHeaders = toNodeOutgoingHttpHeaders(\n              middlewareRes.headers\n            ) as Record<string, string | string[] | undefined>\n\n            debug('middleware res', middlewareRes.status, middlewareHeaders)\n\n            if (middlewareHeaders['x-middleware-override-headers']) {\n              const overriddenHeaders: Set<string> = new Set()\n              let overrideHeaders: string | string[] =\n                middlewareHeaders['x-middleware-override-headers']\n\n              if (typeof overrideHeaders === 'string') {\n                overrideHeaders = overrideHeaders.split(',')\n              }\n\n              for (const key of overrideHeaders) {\n                overriddenHeaders.add(key.trim())\n              }\n              delete middlewareHeaders['x-middleware-override-headers']\n\n              // Delete headers.\n              for (const key of Object.keys(req.headers)) {\n                if (!overriddenHeaders.has(key)) {\n                  delete req.headers[key]\n                }\n              }\n\n              // Update or add headers.\n              for (const key of overriddenHeaders.keys()) {\n                const valueKey = 'x-middleware-request-' + key\n                const newValue = middlewareHeaders[valueKey]\n                const oldValue = req.headers[key]\n\n                if (oldValue !== newValue) {\n                  req.headers[key] = newValue === null ? undefined : newValue\n                }\n                delete middlewareHeaders[valueKey]\n              }\n            }\n\n            if (\n              !middlewareHeaders['x-middleware-rewrite'] &&\n              !middlewareHeaders['x-middleware-next'] &&\n              !middlewareHeaders['location']\n            ) {\n              middlewareHeaders['x-middleware-refresh'] = '1'\n            }\n            delete middlewareHeaders['x-middleware-next']\n\n            for (const [key, value] of Object.entries({\n              ...filterReqHeaders(middlewareHeaders, ipcForbiddenHeaders),\n            })) {\n              if (\n                [\n                  'content-length',\n                  'x-middleware-rewrite',\n                  'x-middleware-redirect',\n                  'x-middleware-refresh',\n                ].includes(key)\n              ) {\n                continue\n              }\n\n              // for set-cookie, the header shouldn't be added to the response\n              // as it's only needed for the request to the middleware function.\n              if (key === 'x-middleware-set-cookie') {\n                req.headers[key] = value\n                continue\n              }\n\n              if (value) {\n                resHeaders[key] = value\n                req.headers[key] = value\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-rewrite']) {\n              const value = middlewareHeaders['x-middleware-rewrite'] as string\n              const destination = getRelativeURL(value, initUrl)\n              resHeaders['x-middleware-rewrite'] = destination\n\n              parsedUrl = url.parse(destination, true)\n\n              if (parsedUrl.protocol) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n\n              if (config.i18n) {\n                const curLocaleResult = normalizeLocalePath(\n                  parsedUrl.pathname || '',\n                  config.i18n.locales\n                )\n\n                if (curLocaleResult.detectedLocale) {\n                  addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n                }\n              }\n            }\n\n            if (middlewareHeaders['location']) {\n              const value = middlewareHeaders['location'] as string\n              const rel = getRelativeURL(value, initUrl)\n              resHeaders['location'] = rel\n              parsedUrl = url.parse(rel, true)\n\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                statusCode: middlewareRes.status,\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-refresh']) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                bodyStream,\n                statusCode: middlewareRes.status,\n              }\n            }\n          }\n        }\n\n        // handle redirect\n        if (\n          ('statusCode' in route || 'permanent' in route) &&\n          route.destination\n        ) {\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: false,\n            destination: route.destination,\n            params: params,\n            query: parsedUrl.query,\n          })\n\n          const { query } = parsedDestination\n          delete (parsedDestination as any).query\n\n          parsedDestination.search = stringifyQuery(req as any, query)\n\n          parsedDestination.pathname = normalizeRepeatedSlashes(\n            parsedDestination.pathname\n          )\n\n          return {\n            finished: true,\n            // @ts-expect-error custom ParsedUrl\n            parsedUrl: parsedDestination,\n            statusCode: getRedirectStatus(route),\n          }\n        }\n\n        // handle headers\n        if (route.headers) {\n          const hasParams = Object.keys(params).length > 0\n          for (const header of route.headers) {\n            let { key, value } = header\n            if (hasParams) {\n              key = compileNonPath(key, params)\n              value = compileNonPath(value, params)\n            }\n\n            if (key.toLowerCase() === 'set-cookie') {\n              if (!Array.isArray(resHeaders[key])) {\n                const val = resHeaders[key]\n                resHeaders[key] = typeof val === 'string' ? [val] : []\n              }\n              ;(resHeaders[key] as string[]).push(value)\n            } else {\n              resHeaders[key] = value\n            }\n          }\n        }\n\n        // handle rewrite\n        if (route.destination) {\n          let rewriteParams = params\n\n          try {\n            // An interception rewrite might reference a dynamic param for a route the user\n            // is currently on, which wouldn't be extractable from the matched route params.\n            // This attempts to extract the dynamic params from the provided router state.\n            if (isInterceptionRouteRewrite(route as Rewrite)) {\n              const stateHeader =\n                req.headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n\n              if (stateHeader) {\n                rewriteParams = {\n                  ...getSelectedParams(\n                    parseAndValidateFlightRouterState(stateHeader)\n                  ),\n                  ...params,\n                }\n              }\n            }\n          } catch (err) {\n            // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n            // so we'll just use the params from the route matcher\n          }\n\n          // We extract the search params of the destination so we can set it on\n          // the response headers. We don't want to use the following\n          // `parsedDestination` as the query object is mutated.\n          const { search: destinationSearch, pathname: destinationPathname } =\n            parseDestination({\n              destination: route.destination,\n              params: rewriteParams,\n              query: parsedUrl.query,\n            })\n\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: true,\n            destination: route.destination,\n            params: rewriteParams,\n            query: parsedUrl.query,\n          })\n\n          if (parsedDestination.protocol) {\n            return {\n              // @ts-expect-error custom ParsedUrl\n              parsedUrl: parsedDestination,\n              finished: true,\n            }\n          }\n\n          // Set the rewrite headers only if this is a RSC request.\n          if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n            // We set the rewritten path and query headers on the response now\n            // that we know that the it's not an external rewrite.\n            if (parsedUrl.pathname !== destinationPathname) {\n              res.setHeader(NEXT_REWRITTEN_PATH_HEADER, destinationPathname)\n            }\n            if (destinationSearch) {\n              res.setHeader(\n                NEXT_REWRITTEN_QUERY_HEADER,\n                // remove the leading ? from the search\n                destinationSearch.slice(1)\n              )\n            }\n          }\n\n          if (config.i18n) {\n            const curLocaleResult = normalizeLocalePath(\n              removePathPrefix(parsedDestination.pathname, config.basePath),\n              config.i18n.locales\n            )\n\n            if (curLocaleResult.detectedLocale) {\n              addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n            }\n          }\n          didRewrite = true\n          parsedUrl.pathname = parsedDestination.pathname\n          Object.assign(parsedUrl.query, parsedDestination.query)\n        }\n\n        // handle check: true\n        if (route.check) {\n          const output = await checkTrue()\n\n          if (output) {\n            return {\n              parsedUrl,\n              resHeaders,\n              finished: true,\n              matchedOutput: output,\n            }\n          }\n        }\n      }\n    }\n\n    for (const route of routes) {\n      const result = await handleRoute(route)\n      if (result) {\n        return result\n      }\n    }\n\n    return {\n      finished,\n      parsedUrl,\n      resHeaders,\n      matchedOutput,\n    }\n  }\n\n  return resolveRoutes\n}\n"], "names": ["getResolveRoutes", "debug", "setupDebug", "fs<PERSON><PERSON><PERSON>", "config", "opts", "renderServer", "renderServerOpts", "ensureMiddleware", "routes", "match", "name", "minimalMode", "headers", "redirects", "rewrites", "beforeFiles", "afterFiles", "check", "fallback", "resolveRoutes", "req", "res", "isUpgradeReq", "invokedOutputs", "finished", "resHeaders", "matchedOutput", "parsedUrl", "url", "parse", "didRewrite", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "normalizeRepeatedSlashes", "statusCode", "protocol", "socket", "encrypted", "includes", "initUrl", "experimental", "trustHostHeader", "host", "port", "formatHostname", "hostname", "addRequestMeta", "query", "getCloneableBody", "maybeAddTrailingSlash", "pathname", "trailingSlash", "skipMiddlewareUrlNormalize", "endsWith", "domainLocale", "defaultLocale", "initialLocaleResult", "undefined", "i18n", "hadTrailingSlash", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathHasPrefix", "basePath", "normalizeLocalePath", "removePathPrefix", "locales", "detectDomainLocale", "domains", "getHostname", "detectedLocale", "startsWith", "addPathPrefix", "checkLocaleApi", "checkTrue", "has", "output", "getItem", "useFileSystemPublicRoutes", "type", "dynamicRoutes", "getDynamicRoutes", "curPathname", "substring", "length", "localeResult", "handleLocale", "route", "page", "params", "pageOutput", "normalizers", "BasePathPathnameNormalizer", "data", "NextDataPathnameNormalizer", "buildId", "handleRoute", "internal", "isDefaultLocale", "missing", "hasParams", "matchHas", "Object", "assign", "exportPathMapRoutes", "exportPathMapRoute", "result", "getMiddlewareMatchers", "normalized", "normalize", "updated", "curLocaleResult", "path", "posix", "join", "locale", "maybeDecodedPathname", "decodeURIComponent", "serverResult", "initialize", "Error", "middlewareRes", "bodyStream", "requestHandler", "err", "response", "status", "body", "ReadableStream", "start", "controller", "enqueue", "close", "e", "isAbortError", "closed", "middlewareHeaders", "toNodeOutgoingHttpHeaders", "overriddenHeaders", "Set", "overrideHeaders", "key", "add", "trim", "keys", "valueKey", "newValue", "oldValue", "value", "entries", "filterReqHeaders", "ipcForbiddenHeaders", "destination", "getRelativeURL", "rel", "parsedDestination", "prepareDestination", "appendParamsToQuery", "search", "stringifyQuery", "getRedirectStatus", "header", "compileNonPath", "toLowerCase", "Array", "isArray", "val", "push", "rewriteParams", "isInterceptionRouteRewrite", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STATE_TREE_HEADER", "getSelectedParams", "parseAndValidateFlightRouterState", "destinationSearch", "destinationPathname", "parseDestination", "RSC_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "slice"], "mappings": ";;;;+BAmDgBA;;;eAAAA;;;4DAzCA;iEACC;8DACM;6BACU;uBACqB;kCACvB;gCACA;wBACW;8BACb;6BACD;gCACM;wBACO;+BACV;+BACD;+BACA;oCACK;qCACC;kCACH;0BACU;0BACA;6BAEZ;oCAMxB;kCAOA;oCAC2B;oDACS;mDACO;;;;;;AAElD,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AAElB,SAASF,iBACdG,SAEC,EACDC,MAA0B,EAC1BC,IAAsC,EACtCC,YAA0B,EAC1BC,gBAA2D,EAC3DC,gBAAkD;IAYlD,MAAMC,SAAkB;QACtB,sCAAsC;QACtC;YAAEC,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAuB;WAE9CN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUU,OAAO;WACzCR,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUW,SAAS;QAE/C,oCAAoC;QACpC;YAAEJ,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAa;WAEpCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACC,WAAW;QAE1D,oCAAoC;QACpC;YAAEN,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAmB;QAE9C,oDAAoD;QACpD,uBAAuB;QACvB;YAAED,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAW;WAElCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACE,UAAU;QAEzD,6DAA6D;QAC7D,oBAAoB;QACpB;YACEC,OAAO;YACPR,OAAO,IAAO,CAAA,CAAC,CAAA;YACfC,MAAM;QACR;WAEIN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACI,QAAQ;KACxD;IAED,eAAeC,cAAc,EAC3BC,GAAG,EACHC,GAAG,EACHC,YAAY,EACZC,cAAc,EAOf;YAgCIH,aACDA;QAzBF,IAAII,WAAW;QACf,IAAIC,aAAgD,CAAC;QACrD,IAAIC,gBAAiC;QACrC,IAAIC,YAAYC,YAAG,CAACC,KAAK,CAACT,IAAIQ,GAAG,IAAI,IAAI;QACzC,IAAIE,aAAa;QAEjB,MAAMC,WAAW,AAACX,CAAAA,IAAIQ,GAAG,IAAI,EAAC,EAAGI,KAAK,CAAC,KAAK;QAC5C,MAAMC,aAAaF,QAAQ,CAAC,EAAE;QAE9B,oEAAoE;QACpE,+DAA+D;QAC/D,wEAAwE;QACxE,WAAW;QACX,IAAIE,8BAAAA,WAAYxB,KAAK,CAAC,cAAc;YAClCkB,YAAYC,YAAG,CAACC,KAAK,CAACK,IAAAA,gCAAwB,EAACd,IAAIQ,GAAG,GAAI;YAC1D,OAAO;gBACLD;gBACAF;gBACAD,UAAU;gBACVW,YAAY;YACd;QACF;QACA,oCAAoC;QACpC,MAAMC,WACJ,CAAChB,wBAAAA,cAAAA,IAAKiB,MAAM,qBAAZ,AAACjB,YAA2BkB,SAAS,OACrClB,+BAAAA,IAAIR,OAAO,CAAC,oBAAoB,qBAAhCQ,6BAAkCmB,QAAQ,CAAC,YACvC,UACA;QAEN,4DAA4D;QAC5D,MAAMC,UAAU,AAACrC,OAAOsC,YAAY,CAASC,eAAe,GACxD,CAAC,QAAQ,EAAEtB,IAAIR,OAAO,CAAC+B,IAAI,IAAI,cAAcvB,IAAIQ,GAAG,EAAE,GACtDxB,KAAKwC,IAAI,GACP,GAAGR,SAAS,GAAG,EAAES,IAAAA,8BAAc,EAACzC,KAAK0C,QAAQ,IAAI,aAAa,CAAC,EAC7D1C,KAAKwC,IAAI,GACRxB,IAAIQ,GAAG,EAAE,GACZR,IAAIQ,GAAG,IAAI;QAEjBmB,IAAAA,2BAAc,EAAC3B,KAAK,WAAWoB;QAC/BO,IAAAA,2BAAc,EAAC3B,KAAK,aAAa;YAAE,GAAGO,UAAUqB,KAAK;QAAC;QACtDD,IAAAA,2BAAc,EAAC3B,KAAK,gBAAgBgB;QAEpC,IAAI,CAACd,cAAc;YACjByB,IAAAA,2BAAc,EAAC3B,KAAK,gBAAgB6B,IAAAA,6BAAgB,EAAC7B;QACvD;QAEA,MAAM8B,wBAAwB,CAACC;YAC7B,IACEhD,OAAOiD,aAAa,IACpB,CAACjD,OAAOkD,0BAA0B,IAClC,CAACF,SAASG,QAAQ,CAAC,MACnB;gBACA,OAAO,GAAGH,SAAS,CAAC,CAAC;YACvB;YACA,OAAOA;QACT;QAEA,IAAII;QACJ,IAAIC;QACJ,IAAIC,sBAEYC;QAEhB,IAAIvD,OAAOwD,IAAI,EAAE;gBACUhC;YAAzB,MAAMiC,oBAAmBjC,sBAAAA,UAAUwB,QAAQ,qBAAlBxB,oBAAoB2B,QAAQ,CAAC;YACtD,MAAMO,cAAcC,IAAAA,4BAAa,EAC/BnC,UAAUwB,QAAQ,IAAI,IACtBhD,OAAO4D,QAAQ;YAEjBN,sBAAsBO,IAAAA,wCAAmB,EACvCC,IAAAA,kCAAgB,EAACtC,UAAUwB,QAAQ,IAAI,KAAKhD,OAAO4D,QAAQ,GAC3D5D,OAAOwD,IAAI,CAACO,OAAO;YAGrBX,eAAeY,IAAAA,sCAAkB,EAC/BhE,OAAOwD,IAAI,CAACS,OAAO,EACnBC,IAAAA,wBAAW,EAAC1C,WAAWP,IAAIR,OAAO;YAEpC4C,gBAAgBD,CAAAA,gCAAAA,aAAcC,aAAa,KAAIrD,OAAOwD,IAAI,CAACH,aAAa;YAExET,IAAAA,2BAAc,EAAC3B,KAAK,iBAAiBoC;YACrCT,IAAAA,2BAAc,EACZ3B,KACA,UACAqC,oBAAoBa,cAAc,IAAId;YAGxC,gDAAgD;YAChD,IACE,CAACC,oBAAoBa,cAAc,IACnC,CAACb,oBAAoBN,QAAQ,CAACoB,UAAU,CAAC,YACzC;gBACA5C,UAAUwB,QAAQ,GAAGqB,IAAAA,4BAAa,EAChCf,oBAAoBN,QAAQ,KAAK,MAC7B,CAAC,CAAC,EAAEK,eAAe,GACnBgB,IAAAA,4BAAa,EACXf,oBAAoBN,QAAQ,IAAI,IAChC,CAAC,CAAC,EAAEK,eAAe,GAEzBK,cAAc1D,OAAO4D,QAAQ,GAAG;gBAGlC,IAAIH,kBAAkB;oBACpBjC,UAAUwB,QAAQ,GAAGD,sBAAsBvB,UAAUwB,QAAQ;gBAC/D;YACF;QACF;QAEA,MAAMsB,iBAAiB,CAACtB;YACtB,IACEhD,OAAOwD,IAAI,IACXR,aAAalB,eACbwB,uCAAAA,oBAAqBa,cAAc,KACnCR,IAAAA,4BAAa,EAACL,oBAAoBN,QAAQ,EAAE,SAC5C;gBACA,OAAO;YACT;QACF;QAEA,eAAeuB;YACb,MAAMvB,WAAWxB,UAAUwB,QAAQ,IAAI;YAEvC,IAAIsB,eAAetB,WAAW;gBAC5B;YACF;YACA,IAAI,EAAC5B,kCAAAA,eAAgBoD,GAAG,CAACxB,YAAW;gBAClC,MAAMyB,SAAS,MAAM1E,UAAU2E,OAAO,CAAC1B;gBAEvC,IAAIyB,QAAQ;oBACV,IACEzE,OAAO2E,yBAAyB,IAChChD,cACC8C,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;wBACA,OAAOH;oBACT;gBACF;YACF;YACA,MAAMI,gBAAgB9E,UAAU+E,gBAAgB;YAChD,IAAIC,cAAcvD,UAAUwB,QAAQ;YAEpC,IAAIhD,OAAO4D,QAAQ,EAAE;gBACnB,IAAI,CAACD,IAAAA,4BAAa,EAACoB,eAAe,IAAI/E,OAAO4D,QAAQ,GAAG;oBACtD;gBACF;gBACAmB,cAAcA,CAAAA,+BAAAA,YAAaC,SAAS,CAAChF,OAAO4D,QAAQ,CAACqB,MAAM,MAAK;YAClE;YACA,MAAMC,eAAenF,UAAUoF,YAAY,CAACJ,eAAe;YAE3D,KAAK,MAAMK,SAASP,cAAe;gBACjC,qCAAqC;gBACrC,kDAAkD;gBAClD,+CAA+C;gBAC/C,8CAA8C;gBAC9C,8BAA8B;gBAC9B,IAAIzD,kCAAAA,eAAgBoD,GAAG,CAACY,MAAMC,IAAI,GAAG;oBACnC;gBACF;gBACA,MAAMC,SAASF,MAAM9E,KAAK,CAAC4E,aAAalC,QAAQ;gBAEhD,IAAIsC,QAAQ;oBACV,MAAMC,aAAa,MAAMxF,UAAU2E,OAAO,CACxCL,IAAAA,4BAAa,EAACe,MAAMC,IAAI,EAAErF,OAAO4D,QAAQ,IAAI;oBAG/C,0CAA0C;oBAC1C,IACE2B,CAAAA,8BAAAA,WAAYX,IAAI,MAAK,cACrBtB,uCAAAA,oBAAqBa,cAAc,GACnC;wBACA;oBACF;oBAEA,IAAIoB,eAAcR,+BAAAA,YAAaX,UAAU,CAAC,iBAAgB;wBACxDxB,IAAAA,2BAAc,EAAC3B,KAAK,iBAAiB;oBACvC;oBAEA,IAAIjB,OAAO2E,yBAAyB,IAAIhD,YAAY;wBAClD,OAAO4D;oBACT;gBACF;YACF;QACF;QAEA,MAAMC,cAAc;YAClB5B,UACE5D,OAAO4D,QAAQ,IAAI5D,OAAO4D,QAAQ,KAAK,MACnC,IAAI6B,oCAA0B,CAACzF,OAAO4D,QAAQ,IAC9CL;YACNmC,MAAM,IAAIC,oCAA0B,CAAC5F,UAAU6F,OAAO;QACxD;QAEA,eAAeC,YACbT,KAAyB;YAEzB,IAAIL,cAAcvD,UAAUwB,QAAQ,IAAI;YAExC,IAAIhD,OAAOwD,IAAI,IAAI4B,MAAMU,QAAQ,EAAE;gBACjC,MAAMrC,mBAAmBsB,YAAY5B,QAAQ,CAAC;gBAE9C,IAAInD,OAAO4D,QAAQ,EAAE;oBACnBmB,cAAcjB,IAAAA,kCAAgB,EAACiB,aAAa/E,OAAO4D,QAAQ;gBAC7D;gBACA,MAAMF,cAAcqB,gBAAgBvD,UAAUwB,QAAQ;gBAEtD,MAAMkC,eAAerB,IAAAA,wCAAmB,EACtCkB,aACA/E,OAAOwD,IAAI,CAACO,OAAO;gBAErB,MAAMgC,kBAAkBb,aAAaf,cAAc,KAAKd;gBAExD,IAAI0C,iBAAiB;oBACnBhB,cACEG,aAAalC,QAAQ,KAAK,OAAOU,cAC7B1D,OAAO4D,QAAQ,GACfS,IAAAA,4BAAa,EACXa,aAAalC,QAAQ,EACrBU,cAAc1D,OAAO4D,QAAQ,GAAG;gBAE1C,OAAO,IAAIF,aAAa;oBACtBqB,cACEA,gBAAgB,MACZ/E,OAAO4D,QAAQ,GACfS,IAAAA,4BAAa,EAACU,aAAa/E,OAAO4D,QAAQ;gBAClD;gBAEA,IAAI,AAACmC,CAAAA,mBAAmBrC,WAAU,KAAMD,kBAAkB;oBACxDsB,cAAchC,sBAAsBgC;gBACtC;YACF;YACA,IAAIO,SAASF,MAAM9E,KAAK,CAACyE;YAEzB,IAAI,AAACK,CAAAA,MAAMZ,GAAG,IAAIY,MAAMY,OAAO,AAAD,KAAMV,QAAQ;gBAC1C,MAAMW,YAAYC,IAAAA,4BAAQ,EACxBjF,KACAO,UAAUqB,KAAK,EACfuC,MAAMZ,GAAG,EACTY,MAAMY,OAAO;gBAEf,IAAIC,WAAW;oBACbE,OAAOC,MAAM,CAACd,QAAQW;gBACxB,OAAO;oBACLX,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IACEvF,UAAUsG,mBAAmB,IAC7BjB,MAAM7E,IAAI,KAAK,oBACf;oBACA,KAAK,MAAM+F,sBAAsBvG,UAAUsG,mBAAmB,CAAE;wBAC9D,MAAME,SAAS,MAAMV,YAAYS;wBAEjC,IAAIC,QAAQ;4BACV,OAAOA;wBACT;oBACF;gBACF;gBAEA,IAAInB,MAAM7E,IAAI,KAAK,0BAA0BiB,UAAUwB,QAAQ,EAAE;wBAC3DjD;oBAAJ,KAAIA,mCAAAA,UAAUyG,qBAAqB,uBAA/BzG,iCAAmCkF,MAAM,EAAE;4BAIzBO;wBAHpB,IAAIiB,aAAajF,UAAUwB,QAAQ;wBAEnC,qCAAqC;wBACrC,MAAMU,eAAc8B,wBAAAA,YAAY5B,QAAQ,qBAApB4B,sBAAsBlF,KAAK,CAACkB,UAAUwB,QAAQ;wBAClE,IAAIU,eAAe8B,YAAY5B,QAAQ,EAAE;4BACvC6C,aAAajB,YAAY5B,QAAQ,CAAC8C,SAAS,CAACD,YAAY;wBAC1D;wBAEA,IAAIE,UAAU;wBACd,IAAInB,YAAYE,IAAI,CAACpF,KAAK,CAACmG,aAAa;4BACtCE,UAAU;4BACV/D,IAAAA,2BAAc,EAAC3B,KAAK,iBAAiB;4BACrCwF,aAAajB,YAAYE,IAAI,CAACgB,SAAS,CAACD,YAAY;wBACtD;wBAEA,IAAIzG,OAAOwD,IAAI,EAAE;4BACf,MAAMoD,kBAAkB/C,IAAAA,wCAAmB,EACzC4C,YACAzG,OAAOwD,IAAI,CAACO,OAAO;4BAGrB,IAAI6C,gBAAgBzC,cAAc,EAAE;gCAClCvB,IAAAA,2BAAc,EAAC3B,KAAK,UAAU2F,gBAAgBzC,cAAc;4BAC9D;wBACF;wBAEA,iEAAiE;wBACjE,aAAa;wBACb,IAAIwC,SAAS;4BACX,IAAIjD,aAAa;gCACf+C,aAAaI,iBAAI,CAACC,KAAK,CAACC,IAAI,CAAC/G,OAAO4D,QAAQ,EAAE6C;4BAChD;4BAEA,2CAA2C;4BAC3CA,aAAa1D,sBAAsB0D;4BAEnCjF,UAAUwB,QAAQ,GAAGyD;wBACvB;oBACF;gBACF;gBAEA,IAAIrB,MAAM7E,IAAI,KAAK,YAAY;oBAC7B,MAAMyC,WAAWxB,UAAUwB,QAAQ,IAAI;oBAEvC,IAAI5B,CAAAA,kCAAAA,eAAgBoD,GAAG,CAACxB,cAAasB,eAAetB,WAAW;wBAC7D;oBACF;oBACA,MAAMyB,SAAS,MAAM1E,UAAU2E,OAAO,CAAC1B;oBAEvC,IACEyB,UACA,CACEzE,CAAAA,OAAOwD,IAAI,KACXF,uCAAAA,oBAAqBa,cAAc,KACnCR,IAAAA,4BAAa,EAACX,UAAU,OAAM,GAEhC;wBACA,IACEhD,OAAO2E,yBAAyB,IAChChD,cACC8C,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;4BACArD,gBAAgBkD;4BAEhB,IAAIA,OAAOuC,MAAM,EAAE;gCACjBpE,IAAAA,2BAAc,EAAC3B,KAAK,UAAUwD,OAAOuC,MAAM;4BAC7C;4BACA,OAAO;gCACLxF;gCACAF;gCACAD,UAAU;gCACVE;4BACF;wBACF;oBACF;gBACF;gBAEA,IAAI,CAACtB,KAAKO,WAAW,IAAI4E,MAAM7E,IAAI,KAAK,cAAc;oBACpD,MAAMD,QAAQP,UAAUyG,qBAAqB;oBAC7C,IAAIS,uBAAuBzF,UAAUwB,QAAQ,IAAI;oBAEjD,IAAI;wBACFiE,uBAAuBC,mBAAmBD;oBAC5C,EAAE,OAAM;oBACN,+CAA+C,GACjD;oBAEA,IACE,yCAAyC;oBACzC3G,CAAAA,yBAAAA,MAAQkB,UAAUwB,QAAQ,EAAE/B,KAAKO,UAAUqB,KAAK,OAChDvC,yBAAAA,MACE2G,sBACA,yCAAyC;oBACzChG,KACAO,UAAUqB,KAAK,IAEjB;wBACA,IAAIzC,kBAAkB;4BACpB,MAAMA,iBAAiBa,IAAIQ,GAAG;wBAChC;wBAEA,MAAM0F,eACJ,OAAMjH,gCAAAA,aAAckH,UAAU,CAACjH;wBAEjC,IAAI,CAACgH,cAAc;4BACjB,MAAM,qBAA4D,CAA5D,IAAIE,MAAM,CAAC,+CAA+C,CAAC,GAA3D,qBAAA;uCAAA;4CAAA;8CAAA;4BAA2D;wBACnE;wBAEAzE,IAAAA,2BAAc,EAAC3B,KAAK,cAAc;wBAClC2B,IAAAA,2BAAc,EAAC3B,KAAK,gBAAgB;wBACpC2B,IAAAA,2BAAc,EAAC3B,KAAK,eAAe,CAAC;wBACpC2B,IAAAA,2BAAc,EAAC3B,KAAK,oBAAoB;wBACxCpB,MAAM,uBAAuBoB,IAAIQ,GAAG,EAAER,IAAIR,OAAO;wBAEjD,IAAI6G,gBAAsC/D;wBAC1C,IAAIgE,aAAyChE;wBAC7C,IAAI;4BACF,IAAI;gCACF,MAAM4D,aAAaK,cAAc,CAACvG,KAAKC,KAAKM;4BAC9C,EAAE,OAAOiG,KAAU;gCACjB,IAAI,CAAE,CAAA,YAAYA,GAAE,KAAM,CAAE,CAAA,cAAcA,IAAIlB,MAAM,AAAD,GAAI;oCACrD,MAAMkB;gCACR;gCACAH,gBAAgBG,IAAIlB,MAAM,CAACmB,QAAQ;gCACnCxG,IAAIc,UAAU,GAAGsF,cAAcK,MAAM;gCAErC,IAAIL,cAAcM,IAAI,EAAE;oCACtBL,aAAaD,cAAcM,IAAI;gCACjC,OAAO,IAAIN,cAAcK,MAAM,EAAE;oCAC/BJ,aAAa,IAAIM,eAAe;wCAC9BC,OAAMC,UAAU;4CACdA,WAAWC,OAAO,CAAC;4CACnBD,WAAWE,KAAK;wCAClB;oCACF;gCACF;4BACF;wBACF,EAAE,OAAOC,GAAG;4BACV,+DAA+D;4BAC/D,iEAAiE;4BACjE,sBAAsB;4BACtB,IAAIC,IAAAA,0BAAY,EAACD,IAAI;gCACnB,OAAO;oCACL1G;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BACA,MAAM6G;wBACR;wBAEA,IAAIhH,IAAIkH,MAAM,IAAIlH,IAAIG,QAAQ,IAAI,CAACiG,eAAe;4BAChD,OAAO;gCACL9F;gCACAF;gCACAD,UAAU;4BACZ;wBACF;wBAEA,MAAMgH,oBAAoBC,IAAAA,iCAAyB,EACjDhB,cAAc7G,OAAO;wBAGvBZ,MAAM,kBAAkByH,cAAcK,MAAM,EAAEU;wBAE9C,IAAIA,iBAAiB,CAAC,gCAAgC,EAAE;4BACtD,MAAME,oBAAiC,IAAIC;4BAC3C,IAAIC,kBACFJ,iBAAiB,CAAC,gCAAgC;4BAEpD,IAAI,OAAOI,oBAAoB,UAAU;gCACvCA,kBAAkBA,gBAAgB5G,KAAK,CAAC;4BAC1C;4BAEA,KAAK,MAAM6G,OAAOD,gBAAiB;gCACjCF,kBAAkBI,GAAG,CAACD,IAAIE,IAAI;4BAChC;4BACA,OAAOP,iBAAiB,CAAC,gCAAgC;4BAEzD,kBAAkB;4BAClB,KAAK,MAAMK,OAAOvC,OAAO0C,IAAI,CAAC5H,IAAIR,OAAO,EAAG;gCAC1C,IAAI,CAAC8H,kBAAkB/D,GAAG,CAACkE,MAAM;oCAC/B,OAAOzH,IAAIR,OAAO,CAACiI,IAAI;gCACzB;4BACF;4BAEA,yBAAyB;4BACzB,KAAK,MAAMA,OAAOH,kBAAkBM,IAAI,GAAI;gCAC1C,MAAMC,WAAW,0BAA0BJ;gCAC3C,MAAMK,WAAWV,iBAAiB,CAACS,SAAS;gCAC5C,MAAME,WAAW/H,IAAIR,OAAO,CAACiI,IAAI;gCAEjC,IAAIM,aAAaD,UAAU;oCACzB9H,IAAIR,OAAO,CAACiI,IAAI,GAAGK,aAAa,OAAOxF,YAAYwF;gCACrD;gCACA,OAAOV,iBAAiB,CAACS,SAAS;4BACpC;wBACF;wBAEA,IACE,CAACT,iBAAiB,CAAC,uBAAuB,IAC1C,CAACA,iBAAiB,CAAC,oBAAoB,IACvC,CAACA,iBAAiB,CAAC,WAAW,EAC9B;4BACAA,iBAAiB,CAAC,uBAAuB,GAAG;wBAC9C;wBACA,OAAOA,iBAAiB,CAAC,oBAAoB;wBAE7C,KAAK,MAAM,CAACK,KAAKO,MAAM,IAAI9C,OAAO+C,OAAO,CAAC;4BACxC,GAAGC,IAAAA,uBAAgB,EAACd,mBAAmBe,0BAAmB,CAAC;wBAC7D,GAAI;4BACF,IACE;gCACE;gCACA;gCACA;gCACA;6BACD,CAAChH,QAAQ,CAACsG,MACX;gCACA;4BACF;4BAEA,gEAAgE;4BAChE,kEAAkE;4BAClE,IAAIA,QAAQ,2BAA2B;gCACrCzH,IAAIR,OAAO,CAACiI,IAAI,GAAGO;gCACnB;4BACF;4BAEA,IAAIA,OAAO;gCACT3H,UAAU,CAACoH,IAAI,GAAGO;gCAClBhI,IAAIR,OAAO,CAACiI,IAAI,GAAGO;4BACrB;wBACF;wBAEA,IAAIZ,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,MAAMY,QAAQZ,iBAAiB,CAAC,uBAAuB;4BACvD,MAAMgB,cAAcC,IAAAA,6BAAc,EAACL,OAAO5G;4BAC1Cf,UAAU,CAAC,uBAAuB,GAAG+H;4BAErC7H,YAAYC,YAAG,CAACC,KAAK,CAAC2H,aAAa;4BAEnC,IAAI7H,UAAUS,QAAQ,EAAE;gCACtB,OAAO;oCACLT;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BAEA,IAAIrB,OAAOwD,IAAI,EAAE;gCACf,MAAMoD,kBAAkB/C,IAAAA,wCAAmB,EACzCrC,UAAUwB,QAAQ,IAAI,IACtBhD,OAAOwD,IAAI,CAACO,OAAO;gCAGrB,IAAI6C,gBAAgBzC,cAAc,EAAE;oCAClCvB,IAAAA,2BAAc,EAAC3B,KAAK,UAAU2F,gBAAgBzC,cAAc;gCAC9D;4BACF;wBACF;wBAEA,IAAIkE,iBAAiB,CAAC,WAAW,EAAE;4BACjC,MAAMY,QAAQZ,iBAAiB,CAAC,WAAW;4BAC3C,MAAMkB,MAAMD,IAAAA,6BAAc,EAACL,OAAO5G;4BAClCf,UAAU,CAAC,WAAW,GAAGiI;4BACzB/H,YAAYC,YAAG,CAACC,KAAK,CAAC6H,KAAK;4BAE3B,OAAO;gCACL/H;gCACAF;gCACAD,UAAU;gCACVW,YAAYsF,cAAcK,MAAM;4BAClC;wBACF;wBAEA,IAAIU,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,OAAO;gCACL7G;gCACAF;gCACAD,UAAU;gCACVkG;gCACAvF,YAAYsF,cAAcK,MAAM;4BAClC;wBACF;oBACF;gBACF;gBAEA,kBAAkB;gBAClB,IACE,AAAC,CAAA,gBAAgBvC,SAAS,eAAeA,KAAI,KAC7CA,MAAMiE,WAAW,EACjB;oBACA,MAAM,EAAEG,iBAAiB,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;wBAC/CC,qBAAqB;wBACrBL,aAAajE,MAAMiE,WAAW;wBAC9B/D,QAAQA;wBACRzC,OAAOrB,UAAUqB,KAAK;oBACxB;oBAEA,MAAM,EAAEA,KAAK,EAAE,GAAG2G;oBAClB,OAAO,AAACA,kBAA0B3G,KAAK;oBAEvC2G,kBAAkBG,MAAM,GAAGC,IAAAA,gCAAc,EAAC3I,KAAY4B;oBAEtD2G,kBAAkBxG,QAAQ,GAAGjB,IAAAA,gCAAwB,EACnDyH,kBAAkBxG,QAAQ;oBAG5B,OAAO;wBACL3B,UAAU;wBACV,oCAAoC;wBACpCG,WAAWgI;wBACXxH,YAAY6H,IAAAA,iCAAiB,EAACzE;oBAChC;gBACF;gBAEA,iBAAiB;gBACjB,IAAIA,MAAM3E,OAAO,EAAE;oBACjB,MAAMwF,YAAYE,OAAO0C,IAAI,CAACvD,QAAQL,MAAM,GAAG;oBAC/C,KAAK,MAAM6E,UAAU1E,MAAM3E,OAAO,CAAE;wBAClC,IAAI,EAAEiI,GAAG,EAAEO,KAAK,EAAE,GAAGa;wBACrB,IAAI7D,WAAW;4BACbyC,MAAMqB,IAAAA,kCAAc,EAACrB,KAAKpD;4BAC1B2D,QAAQc,IAAAA,kCAAc,EAACd,OAAO3D;wBAChC;wBAEA,IAAIoD,IAAIsB,WAAW,OAAO,cAAc;4BACtC,IAAI,CAACC,MAAMC,OAAO,CAAC5I,UAAU,CAACoH,IAAI,GAAG;gCACnC,MAAMyB,MAAM7I,UAAU,CAACoH,IAAI;gCAC3BpH,UAAU,CAACoH,IAAI,GAAG,OAAOyB,QAAQ,WAAW;oCAACA;iCAAI,GAAG,EAAE;4BACxD;;4BACE7I,UAAU,CAACoH,IAAI,CAAc0B,IAAI,CAACnB;wBACtC,OAAO;4BACL3H,UAAU,CAACoH,IAAI,GAAGO;wBACpB;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,IAAI7D,MAAMiE,WAAW,EAAE;oBACrB,IAAIgB,gBAAgB/E;oBAEpB,IAAI;wBACF,+EAA+E;wBAC/E,gFAAgF;wBAChF,8EAA8E;wBAC9E,IAAIgF,IAAAA,8DAA0B,EAAClF,QAAmB;4BAChD,MAAMmF,cACJtJ,IAAIR,OAAO,CAAC+J,+CAA6B,CAACR,WAAW,GAAG;4BAE1D,IAAIO,aAAa;gCACfF,gBAAgB;oCACd,GAAGI,IAAAA,qCAAiB,EAClBC,IAAAA,oEAAiC,EAACH,aACnC;oCACD,GAAGjF,MAAM;gCACX;4BACF;wBACF;oBACF,EAAE,OAAOmC,KAAK;oBACZ,wFAAwF;oBACxF,sDAAsD;oBACxD;oBAEA,sEAAsE;oBACtE,2DAA2D;oBAC3D,sDAAsD;oBACtD,MAAM,EAAEkC,QAAQgB,iBAAiB,EAAE3H,UAAU4H,mBAAmB,EAAE,GAChEC,IAAAA,oCAAgB,EAAC;wBACfxB,aAAajE,MAAMiE,WAAW;wBAC9B/D,QAAQ+E;wBACRxH,OAAOrB,UAAUqB,KAAK;oBACxB;oBAEF,MAAM,EAAE2G,iBAAiB,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;wBAC/CC,qBAAqB;wBACrBL,aAAajE,MAAMiE,WAAW;wBAC9B/D,QAAQ+E;wBACRxH,OAAOrB,UAAUqB,KAAK;oBACxB;oBAEA,IAAI2G,kBAAkBvH,QAAQ,EAAE;wBAC9B,OAAO;4BACL,oCAAoC;4BACpCT,WAAWgI;4BACXnI,UAAU;wBACZ;oBACF;oBAEA,yDAAyD;oBACzD,IAAIJ,IAAIR,OAAO,CAACqK,4BAAU,CAACd,WAAW,GAAG,KAAK,KAAK;wBACjD,kEAAkE;wBAClE,sDAAsD;wBACtD,IAAIxI,UAAUwB,QAAQ,KAAK4H,qBAAqB;4BAC9C1J,IAAI6J,SAAS,CAACC,4CAA0B,EAAEJ;wBAC5C;wBACA,IAAID,mBAAmB;4BACrBzJ,IAAI6J,SAAS,CACXE,6CAA2B,EAC3B,uCAAuC;4BACvCN,kBAAkBO,KAAK,CAAC;wBAE5B;oBACF;oBAEA,IAAIlL,OAAOwD,IAAI,EAAE;wBACf,MAAMoD,kBAAkB/C,IAAAA,wCAAmB,EACzCC,IAAAA,kCAAgB,EAAC0F,kBAAkBxG,QAAQ,EAAEhD,OAAO4D,QAAQ,GAC5D5D,OAAOwD,IAAI,CAACO,OAAO;wBAGrB,IAAI6C,gBAAgBzC,cAAc,EAAE;4BAClCvB,IAAAA,2BAAc,EAAC3B,KAAK,UAAU2F,gBAAgBzC,cAAc;wBAC9D;oBACF;oBACAxC,aAAa;oBACbH,UAAUwB,QAAQ,GAAGwG,kBAAkBxG,QAAQ;oBAC/CmD,OAAOC,MAAM,CAAC5E,UAAUqB,KAAK,EAAE2G,kBAAkB3G,KAAK;gBACxD;gBAEA,qBAAqB;gBACrB,IAAIuC,MAAMtE,KAAK,EAAE;oBACf,MAAM2D,SAAS,MAAMF;oBAErB,IAAIE,QAAQ;wBACV,OAAO;4BACLjD;4BACAF;4BACAD,UAAU;4BACVE,eAAekD;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,KAAK,MAAMW,SAAS/E,OAAQ;YAC1B,MAAMkG,SAAS,MAAMV,YAAYT;YACjC,IAAImB,QAAQ;gBACV,OAAOA;YACT;QACF;QAEA,OAAO;YACLlF;YACAG;YACAF;YACAC;QACF;IACF;IAEA,OAAOP;AACT"}