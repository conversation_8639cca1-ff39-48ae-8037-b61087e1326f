{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { Duplex } from 'stream'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type getBaseWebpackConfig from '../../build/webpack-config'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Project, Update as TurbopackUpdate } from '../../build/swc/types'\nimport type { VersionInfo } from './parse-version-info'\nimport type { DebugInfo } from '../../client/components/react-dev-overlay/types'\nimport type { DevIndicatorServerState } from './dev-indicator-server-state'\n\nexport const enum HMR_ACTIONS_SENT_TO_BROWSER {\n  ADDED_PAGE = 'addedPage',\n  REMOVED_PAGE = 'removedPage',\n  RELOAD_PAGE = 'reloadPage',\n  SERVER_COMPONENT_CHANGES = 'serverComponentChanges',\n  MIDDLEWARE_CHANGES = 'middlewareChanges',\n  CLIENT_CHANGES = 'clientChanges',\n  SERVER_ONLY_CHANGES = 'serverOnlyChanges',\n  SYNC = 'sync',\n  BUILT = 'built',\n  BUILDING = 'building',\n  DEV_PAGES_MANIFEST_UPDATE = 'devPagesManifestUpdate',\n  TURBOPACK_MESSAGE = 'turbopack-message',\n  SERVER_ERROR = 'serverError',\n  TURBOPACK_CONNECTED = 'turbopack-connected',\n  ISR_MANIFEST = 'isrManifest',\n  DEV_INDICATOR = 'devIndicator',\n}\n\ninterface ServerErrorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR\n  errorJSON: string\n}\n\nexport interface TurbopackMessageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE\n  data: TurbopackUpdate | TurbopackUpdate[]\n}\n\ninterface BuildingAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING\n}\n\nexport interface CompilationError {\n  moduleName?: string\n  message: string\n  details?: string\n  moduleTrace?: Array<{ moduleName?: string }>\n  stack?: string\n}\nexport interface SyncAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  versionInfo: VersionInfo\n  updatedModules?: ReadonlyArray<string>\n  debug?: DebugInfo\n  devIndicator: DevIndicatorServerState\n}\ninterface BuiltAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  updatedModules?: ReadonlyArray<string>\n}\n\ninterface AddedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE\n  data: [page: string | null]\n}\n\ninterface RemovedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE\n  data: [page: string | null]\n}\n\nexport interface ReloadPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE\n  data: string\n}\n\ninterface ServerComponentChangesAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES\n  hash: string\n}\n\ninterface MiddlewareChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES\n}\n\ninterface ClientChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES\n}\n\ninterface ServerOnlyChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES\n  pages: ReadonlyArray<string>\n}\n\ninterface DevPagesManifestUpdateAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n  data: [\n    {\n      devPagesManifest: true\n    },\n  ]\n}\n\nexport interface TurbopackConnectedAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n  data: { sessionId: number }\n}\n\nexport interface AppIsrManifestAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST\n  data: Record<string, boolean>\n}\n\nexport interface DevIndicatorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type HMR_ACTION_TYPES =\n  | TurbopackMessageAction\n  | TurbopackConnectedAction\n  | BuildingAction\n  | SyncAction\n  | BuiltAction\n  | AddedPageAction\n  | RemovedPageAction\n  | ReloadPageAction\n  | ServerComponentChangesAction\n  | ClientChangesAction\n  | MiddlewareChangesAction\n  | ServerOnlyChangesAction\n  | DevPagesManifestUpdateAction\n  | ServerErrorAction\n  | AppIsrManifestAction\n  | DevIndicatorAction\n\nexport type TurbopackMsgToBrowser =\n  | { type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE; data: any }\n  | {\n      type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      data: { sessionId: number }\n    }\n\nexport interface NextJsHotReloaderInterface {\n  turbopackProject?: Project\n  activeWebpackConfigs?: Array<Awaited<ReturnType<typeof getBaseWebpackConfig>>>\n  serverStats: webpack.Stats | null\n  edgeServerStats: webpack.Stats | null\n  run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }>\n\n  setHmrServerError(error: Error | null): void\n  clearHmrServerError(): void\n  start(): Promise<void>\n  send(action: HMR_ACTION_TYPES): void\n  getCompilationErrors(page: string): Promise<any[]>\n  onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    onUpgrade: (client: { send(data: string): void }) => void\n  ): void\n  invalidate({\n    reloadAfterInvalidation,\n  }: {\n    reloadAfterInvalidation: boolean\n  }): Promise<void> | void\n  buildFallbackError(): Promise<void>\n  ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void>\n  close(): void\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER"], "mappings": ";;;;+BAWkBA;;;eAAAA;;;AAAX,IAAA,AAAWA,qDAAAA;;;;;;;;;;;;;;;;;WAAAA"}