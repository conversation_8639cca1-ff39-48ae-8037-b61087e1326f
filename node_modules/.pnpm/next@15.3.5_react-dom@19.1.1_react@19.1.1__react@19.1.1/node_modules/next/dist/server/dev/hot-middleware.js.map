{"version": 3, "sources": ["../../../src/server/dev/hot-middleware.ts"], "sourcesContent": ["// Based on https://github.com/webpack-contrib/webpack-hot-middleware/blob/9708d781ae0e46179cf8ea1a94719de4679aaf53/middleware.js\n// Included License below\n\n// Copyright JS Foundation and other contributors\n\n// Permission is hereby granted, free of charge, to any person obtaining\n// a copy of this software and associated documentation files (the\n// 'Software'), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to\n// the following conditions:\n\n// The above copyright notice and this permission notice shall be\n// included in all copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n// IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n// CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n// SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type ws from 'next/dist/compiled/ws'\nimport { isMiddlewareFilename } from '../../build/utils'\nimport type { VersionInfo } from './parse-version-info'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\n\nfunction isMiddlewareStats(stats: webpack.Stats) {\n  for (const key of stats.compilation.entrypoints.keys()) {\n    if (isMiddlewareFilename(key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction statsToJson(stats?: webpack.Stats | null) {\n  if (!stats) return {}\n  return stats.toJson({\n    all: false,\n    errors: true,\n    hash: true,\n    warnings: true,\n  })\n}\n\nfunction getStatsForSyncEvent(\n  clientStats: { ts: number; stats: webpack.Stats } | null,\n  serverStats: { ts: number; stats: webpack.Stats } | null\n) {\n  if (!clientStats) return serverStats?.stats\n  if (!serverStats) return clientStats?.stats\n\n  // Prefer the server compiler stats if it has errors.\n  // Otherwise we may end up in a state where the client compilation is the latest but without errors.\n  // This causes the error overlay to not display the build error.\n  if (serverStats.stats.hasErrors()) {\n    return serverStats.stats\n  }\n\n  // Return the latest stats\n  return serverStats.ts > clientStats.ts ? serverStats.stats : clientStats.stats\n}\n\nclass EventStream {\n  clients: Set<ws>\n  constructor() {\n    this.clients = new Set()\n  }\n\n  close() {\n    for (const wsClient of this.clients) {\n      // it's okay to not cleanly close these websocket connections, this is dev\n      wsClient.terminate()\n    }\n    this.clients.clear()\n  }\n\n  handler(client: ws) {\n    this.clients.add(client)\n    client.addEventListener('close', () => {\n      this.clients.delete(client)\n    })\n  }\n\n  publish(payload: any) {\n    for (const wsClient of this.clients) {\n      wsClient.send(JSON.stringify(payload))\n    }\n  }\n}\n\nexport class WebpackHotMiddleware {\n  eventStream: EventStream\n  clientLatestStats: { ts: number; stats: webpack.Stats } | null\n  middlewareLatestStats: { ts: number; stats: webpack.Stats } | null\n  serverLatestStats: { ts: number; stats: webpack.Stats } | null\n  closed: boolean\n  versionInfo: VersionInfo\n  devtoolsFrontendUrl: string | undefined\n\n  constructor(\n    compilers: webpack.Compiler[],\n    versionInfo: VersionInfo,\n    devtoolsFrontendUrl: string | undefined\n  ) {\n    this.eventStream = new EventStream()\n    this.clientLatestStats = null\n    this.middlewareLatestStats = null\n    this.serverLatestStats = null\n    this.closed = false\n    this.versionInfo = versionInfo\n    this.devtoolsFrontendUrl = devtoolsFrontendUrl\n\n    compilers[0].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onClientInvalid\n    )\n    compilers[0].hooks.done.tap('webpack-hot-middleware', this.onClientDone)\n    compilers[1].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onServerInvalid\n    )\n    compilers[1].hooks.done.tap('webpack-hot-middleware', this.onServerDone)\n    compilers[2].hooks.done.tap('webpack-hot-middleware', this.onEdgeServerDone)\n    compilers[2].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onEdgeServerInvalid\n    )\n  }\n\n  onClientInvalid = () => {\n    if (this.closed || this.serverLatestStats?.stats.hasErrors()) return\n    this.publish({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING,\n    })\n  }\n\n  onClientDone = (statsResult: webpack.Stats) => {\n    this.clientLatestStats = { ts: Date.now(), stats: statsResult }\n    if (this.closed || this.serverLatestStats?.stats.hasErrors()) return\n    this.publishStats(statsResult)\n  }\n\n  onServerInvalid = () => {\n    if (!this.serverLatestStats?.stats.hasErrors()) return\n    this.serverLatestStats = null\n    if (this.clientLatestStats?.stats) {\n      this.publishStats(this.clientLatestStats.stats)\n    }\n  }\n\n  onServerDone = (statsResult: webpack.Stats) => {\n    if (this.closed) return\n    if (statsResult.hasErrors()) {\n      this.serverLatestStats = { ts: Date.now(), stats: statsResult }\n      this.publishStats(statsResult)\n    }\n  }\n\n  onEdgeServerInvalid = () => {\n    if (!this.middlewareLatestStats?.stats.hasErrors()) return\n    this.middlewareLatestStats = null\n    if (this.clientLatestStats?.stats) {\n      this.publishStats(this.clientLatestStats.stats)\n    }\n  }\n\n  onEdgeServerDone = (statsResult: webpack.Stats) => {\n    if (!isMiddlewareStats(statsResult)) {\n      this.onServerInvalid()\n      this.onServerDone(statsResult)\n      return\n    }\n\n    if (statsResult.hasErrors()) {\n      this.middlewareLatestStats = { ts: Date.now(), stats: statsResult }\n      this.publishStats(statsResult)\n    }\n  }\n\n  /**\n   * To sync we use the most recent stats but also we append middleware\n   * errors. This is because it is possible that middleware fails to compile\n   * and we still want to show the client overlay with the error while\n   * the error page should be rendered just fine.\n   */\n  onHMR = (client: ws) => {\n    if (this.closed) return\n    this.eventStream.handler(client)\n\n    const syncStats = getStatsForSyncEvent(\n      this.clientLatestStats,\n      this.serverLatestStats\n    )\n\n    if (syncStats) {\n      const stats = statsToJson(syncStats)\n      const middlewareStats = statsToJson(this.middlewareLatestStats?.stats)\n\n      if (devIndicatorServerState.disabledUntil < Date.now()) {\n        devIndicatorServerState.disabledUntil = 0\n      }\n\n      this.publish({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n        hash: stats.hash!,\n        errors: [...(stats.errors || []), ...(middlewareStats.errors || [])],\n        warnings: [\n          ...(stats.warnings || []),\n          ...(middlewareStats.warnings || []),\n        ],\n        versionInfo: this.versionInfo,\n        debug: {\n          devtoolsFrontendUrl: this.devtoolsFrontendUrl,\n        },\n        devIndicator: devIndicatorServerState,\n      })\n    }\n  }\n\n  publishStats = (statsResult: webpack.Stats) => {\n    const stats = statsResult.toJson({\n      all: false,\n      hash: true,\n      warnings: true,\n      errors: true,\n      moduleTrace: true,\n    })\n\n    this.publish({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n      hash: stats.hash!,\n      warnings: stats.warnings || [],\n      errors: stats.errors || [],\n    })\n  }\n\n  publish = (payload: HMR_ACTION_TYPES) => {\n    if (this.closed) return\n    this.eventStream.publish(payload)\n  }\n  close = () => {\n    if (this.closed) return\n    // Can't remove compiler plugins, so we just set a flag and noop if closed\n    // https://github.com/webpack/tapable/issues/32#issuecomment-350644466\n    this.closed = true\n    this.eventStream.close()\n  }\n}\n"], "names": ["WebpackHotMiddleware", "isMiddlewareStats", "stats", "key", "compilation", "entrypoints", "keys", "isMiddlewareFilename", "stats<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "all", "errors", "hash", "warnings", "getStatsForSyncEvent", "clientStats", "serverStats", "hasErrors", "ts", "EventStream", "constructor", "clients", "Set", "close", "wsClient", "terminate", "clear", "handler", "client", "add", "addEventListener", "delete", "publish", "payload", "send", "JSON", "stringify", "compilers", "versionInfo", "devtoolsFrontendUrl", "onClientInvalid", "closed", "serverLatestStats", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "onClientDone", "statsResult", "clientLatestStats", "Date", "now", "publishStats", "onServerInvalid", "onServerDone", "onEdgeServerInvalid", "middlewareLatestStats", "onEdgeServerDone", "onHMR", "eventStream", "syncStats", "middlewareStats", "devIndicatorServerState", "disabledUntil", "SYNC", "debug", "devIndicator", "moduleTrace", "BUILT", "hooks", "invalid", "tap", "done"], "mappings": "AAAA,iIAAiI;AACjI,yBAAyB;AAEzB,iDAAiD;AAEjD,wEAAwE;AACxE,kEAAkE;AAClE,sEAAsE;AACtE,sEAAsE;AACtE,qEAAqE;AACrE,wEAAwE;AACxE,4BAA4B;AAE5B,iEAAiE;AACjE,kEAAkE;AAElE,kEAAkE;AAClE,qEAAqE;AACrE,yEAAyE;AACzE,uEAAuE;AACvE,uEAAuE;AACvE,oEAAoE;AACpE,yDAAyD;;;;;+BA2E5CA;;;eAAAA;;;uBAxEwB;kCAGO;yCACJ;AAExC,SAASC,kBAAkBC,KAAoB;IAC7C,KAAK,MAAMC,OAAOD,MAAME,WAAW,CAACC,WAAW,CAACC,IAAI,GAAI;QACtD,IAAIC,IAAAA,2BAAoB,EAACJ,MAAM;YAC7B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASK,YAAYN,KAA4B;IAC/C,IAAI,CAACA,OAAO,OAAO,CAAC;IACpB,OAAOA,MAAMO,MAAM,CAAC;QAClBC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,UAAU;IACZ;AACF;AAEA,SAASC,qBACPC,WAAwD,EACxDC,WAAwD;IAExD,IAAI,CAACD,aAAa,OAAOC,+BAAAA,YAAad,KAAK;IAC3C,IAAI,CAACc,aAAa,OAAOD,+BAAAA,YAAab,KAAK;IAE3C,qDAAqD;IACrD,oGAAoG;IACpG,gEAAgE;IAChE,IAAIc,YAAYd,KAAK,CAACe,SAAS,IAAI;QACjC,OAAOD,YAAYd,KAAK;IAC1B;IAEA,0BAA0B;IAC1B,OAAOc,YAAYE,EAAE,GAAGH,YAAYG,EAAE,GAAGF,YAAYd,KAAK,GAAGa,YAAYb,KAAK;AAChF;AAEA,MAAMiB;IAEJC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAG,IAAIC;IACrB;IAEAC,QAAQ;QACN,KAAK,MAAMC,YAAY,IAAI,CAACH,OAAO,CAAE;YACnC,0EAA0E;YAC1EG,SAASC,SAAS;QACpB;QACA,IAAI,CAACJ,OAAO,CAACK,KAAK;IACpB;IAEAC,QAAQC,MAAU,EAAE;QAClB,IAAI,CAACP,OAAO,CAACQ,GAAG,CAACD;QACjBA,OAAOE,gBAAgB,CAAC,SAAS;YAC/B,IAAI,CAACT,OAAO,CAACU,MAAM,CAACH;QACtB;IACF;IAEAI,QAAQC,OAAY,EAAE;QACpB,KAAK,MAAMT,YAAY,IAAI,CAACH,OAAO,CAAE;YACnCG,SAASU,IAAI,CAACC,KAAKC,SAAS,CAACH;QAC/B;IACF;AACF;AAEO,MAAMjC;IASXoB,YACEiB,SAA6B,EAC7BC,WAAwB,EACxBC,mBAAuC,CACvC;aA0BFC,kBAAkB;gBACG;YAAnB,IAAI,IAAI,CAACC,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBxC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACe,OAAO,CAAC;gBACXW,QAAQC,6CAA2B,CAACC,QAAQ;YAC9C;QACF;aAEAC,eAAe,CAACC;gBAEK;YADnB,IAAI,CAACC,iBAAiB,GAAG;gBAAE9B,IAAI+B,KAAKC,GAAG;gBAAIhD,OAAO6C;YAAY;YAC9D,IAAI,IAAI,CAACN,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBxC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACkC,YAAY,CAACJ;QACpB;aAEAK,kBAAkB;gBACX,yBAED;YAFJ,IAAI,GAAC,0BAAA,IAAI,CAACV,iBAAiB,qBAAtB,wBAAwBxC,KAAK,CAACe,SAAS,KAAI;YAChD,IAAI,CAACyB,iBAAiB,GAAG;YACzB,KAAI,0BAAA,IAAI,CAACM,iBAAiB,qBAAtB,wBAAwB9C,KAAK,EAAE;gBACjC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC9C,KAAK;YAChD;QACF;aAEAmD,eAAe,CAACN;YACd,IAAI,IAAI,CAACN,MAAM,EAAE;YACjB,IAAIM,YAAY9B,SAAS,IAAI;gBAC3B,IAAI,CAACyB,iBAAiB,GAAG;oBAAExB,IAAI+B,KAAKC,GAAG;oBAAIhD,OAAO6C;gBAAY;gBAC9D,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;aAEAO,sBAAsB;gBACf,6BAED;YAFJ,IAAI,GAAC,8BAAA,IAAI,CAACC,qBAAqB,qBAA1B,4BAA4BrD,KAAK,CAACe,SAAS,KAAI;YACpD,IAAI,CAACsC,qBAAqB,GAAG;YAC7B,KAAI,0BAAA,IAAI,CAACP,iBAAiB,qBAAtB,wBAAwB9C,KAAK,EAAE;gBACjC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC9C,KAAK;YAChD;QACF;aAEAsD,mBAAmB,CAACT;YAClB,IAAI,CAAC9C,kBAAkB8C,cAAc;gBACnC,IAAI,CAACK,eAAe;gBACpB,IAAI,CAACC,YAAY,CAACN;gBAClB;YACF;YAEA,IAAIA,YAAY9B,SAAS,IAAI;gBAC3B,IAAI,CAACsC,qBAAqB,GAAG;oBAAErC,IAAI+B,KAAKC,GAAG;oBAAIhD,OAAO6C;gBAAY;gBAClE,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;QAEA;;;;;GAKC,QACDU,QAAQ,CAAC7B;YACP,IAAI,IAAI,CAACa,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAAC/B,OAAO,CAACC;YAEzB,MAAM+B,YAAY7C,qBAChB,IAAI,CAACkC,iBAAiB,EACtB,IAAI,CAACN,iBAAiB;YAGxB,IAAIiB,WAAW;oBAEuB;gBADpC,MAAMzD,QAAQM,YAAYmD;gBAC1B,MAAMC,kBAAkBpD,aAAY,8BAAA,IAAI,CAAC+C,qBAAqB,qBAA1B,4BAA4BrD,KAAK;gBAErE,IAAI2D,gDAAuB,CAACC,aAAa,GAAGb,KAAKC,GAAG,IAAI;oBACtDW,gDAAuB,CAACC,aAAa,GAAG;gBAC1C;gBAEA,IAAI,CAAC9B,OAAO,CAAC;oBACXW,QAAQC,6CAA2B,CAACmB,IAAI;oBACxCnD,MAAMV,MAAMU,IAAI;oBAChBD,QAAQ;2BAAKT,MAAMS,MAAM,IAAI,EAAE;2BAAOiD,gBAAgBjD,MAAM,IAAI,EAAE;qBAAE;oBACpEE,UAAU;2BACJX,MAAMW,QAAQ,IAAI,EAAE;2BACpB+C,gBAAgB/C,QAAQ,IAAI,EAAE;qBACnC;oBACDyB,aAAa,IAAI,CAACA,WAAW;oBAC7B0B,OAAO;wBACLzB,qBAAqB,IAAI,CAACA,mBAAmB;oBAC/C;oBACA0B,cAAcJ,gDAAuB;gBACvC;YACF;QACF;aAEAV,eAAe,CAACJ;YACd,MAAM7C,QAAQ6C,YAAYtC,MAAM,CAAC;gBAC/BC,KAAK;gBACLE,MAAM;gBACNC,UAAU;gBACVF,QAAQ;gBACRuD,aAAa;YACf;YAEA,IAAI,CAAClC,OAAO,CAAC;gBACXW,QAAQC,6CAA2B,CAACuB,KAAK;gBACzCvD,MAAMV,MAAMU,IAAI;gBAChBC,UAAUX,MAAMW,QAAQ,IAAI,EAAE;gBAC9BF,QAAQT,MAAMS,MAAM,IAAI,EAAE;YAC5B;QACF;aAEAqB,UAAU,CAACC;YACT,IAAI,IAAI,CAACQ,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAAC1B,OAAO,CAACC;QAC3B;aACAV,QAAQ;YACN,IAAI,IAAI,CAACkB,MAAM,EAAE;YACjB,0EAA0E;YAC1E,sEAAsE;YACtE,IAAI,CAACA,MAAM,GAAG;YACd,IAAI,CAACiB,WAAW,CAACnC,KAAK;QACxB;QA9IE,IAAI,CAACmC,WAAW,GAAG,IAAIvC;QACvB,IAAI,CAAC6B,iBAAiB,GAAG;QACzB,IAAI,CAACO,qBAAqB,GAAG;QAC7B,IAAI,CAACb,iBAAiB,GAAG;QACzB,IAAI,CAACD,MAAM,GAAG;QACd,IAAI,CAACH,WAAW,GAAGA;QACnB,IAAI,CAACC,mBAAmB,GAAGA;QAE3BF,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAC9B,eAAe;QAEtBH,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACxB,YAAY;QACvET,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAClB,eAAe;QAEtBf,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACjB,YAAY;QACvEhB,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACd,gBAAgB;QAC3EnB,SAAS,CAAC,EAAE,CAAC+B,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAChB,mBAAmB;IAE5B;AAwHF"}