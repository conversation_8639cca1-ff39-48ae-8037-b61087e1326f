{
	"extends": "@cloudflare/workers-tsconfig/tsconfig.json",
	"compilerOptions": {
		"types": ["@cloudflare/workers-types"]
	},
	"include": ["**/*.ts"],
	"exclude": [
		"__tests__",
		"./init-tests/**",
		// Note: `startDevWorker` and `middleware` should also be included but some work is needed
		//       for that first (see: https://github.com/cloudflare/workers-sdk/issues/8303)
		"startDevWorker",
		"middleware"
	]
}
