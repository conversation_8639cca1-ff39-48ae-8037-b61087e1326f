{"name": "wrangler", "version": "4.26.0", "description": "Command-line interface for all things Cloudflare Workers", "keywords": ["wrangler", "cloudflare", "workers", "cloudflare workers", "edge", "compute", "serverless", "serverless application", "serverless module", "wasm", "web", "assembly", "webassembly", "rust", "emscripten", "typescript", "graphql", "router", "http", "cli"], "homepage": "https://github.com/cloudflare/workers-sdk#readme", "bugs": {"url": "https://github.com/cloudflare/workers-sdk/issues"}, "repository": {"type": "git", "url": "https://github.com/cloudflare/workers-sdk.git", "directory": "packages/wrangler"}, "license": "MIT OR Apache-2.0", "author": "<EMAIL>", "main": "wrangler-dist/cli.js", "types": "wrangler-dist/cli.d.ts", "bin": {"wrangler": "./bin/wrangler.js", "wrangler2": "./bin/wrangler.js"}, "files": ["bin", "miniflare-dist", "wrangler-dist", "templates", "kv-asset-handler.js", "config-schema.json"], "dependencies": {"blake3-wasm": "2.1.5", "esbuild": "0.25.4", "path-to-regexp": "6.3.0", "unenv": "2.0.0-rc.17", "workerd": "1.20250712.0", "@cloudflare/kv-asset-handler": "0.4.0", "@cloudflare/unenv-preset": "2.4.1", "miniflare": "4.20250712.2"}, "devDependencies": {"@aws-sdk/client-s3": "^3.721.0", "@cloudflare/types": "6.18.4", "@cloudflare/workers-types": "^4.20250712.0", "@cspotcode/source-map-support": "0.8.1", "@iarna/toml": "^3.0.0", "@sentry/node": "^7.86.0", "@sentry/types": "^7.86.0", "@sentry/utils": "^7.86.0", "@types/command-exists": "^1.2.0", "@types/glob-to-regexp": "^0.4.1", "@types/is-ci": "^3.0.0", "@types/javascript-time-ago": "^2.0.3", "@types/mime": "^3.0.4", "@types/minimatch": "^5.1.2", "@types/node": "^20.17.32", "@types/node-forge": "^1.3.11", "@types/prompts": "^2.0.14", "@types/resolve": "^1.20.6", "@types/shell-quote": "^1.7.2", "@types/signal-exit": "^3.0.1", "@types/supports-color": "^8.1.1", "@types/ws": "^8.5.7", "@types/yargs": "^17.0.22", "@vitest/ui": "~3.2.0", "@webcontainer/env": "^1.1.0", "chalk": "^5.2.0", "chokidar": "^4.0.1", "cli-table3": "^0.6.3", "cmd-shim": "^4.1.0", "command-exists": "^1.2.9", "concurrently": "^8.2.2", "date-fns": "^4.1.0", "devtools-protocol": "^0.0.1182435", "dotenv": "^16.3.1", "execa": "^6.1.0", "find-up": "^6.3.0", "get-port": "^7.0.0", "glob-to-regexp": "^0.4.1", "https-proxy-agent": "7.0.2", "is-ci": "^3.0.1", "itty-time": "^1.0.6", "javascript-time-ago": "^2.5.4", "md5-file": "5.0.0", "mime": "^3.0.0", "minimatch": "^5.1.0", "mock-socket": "^9.3.1", "msw": "2.4.3", "node-forge": "^1.3.1", "open": "^8.4.0", "p-queue": "^7.2.0", "patch-console": "^1.0.0", "pretty-bytes": "^6.0.0", "prompts": "^2.4.2", "resolve": "^1.22.8", "rimraf": "^6.0.1", "selfsigned": "^2.0.1", "semiver": "^1.1.0", "shell-quote": "^1.8.1", "signal-exit": "^3.0.7", "source-map": "^0.6.1", "strip-ansi": "^7.1.0", "supports-color": "^9.2.2", "timeago.js": "^4.0.2", "ts-dedent": "^2.2.0", "ts-json-schema-generator": "^1.5.0", "tsup": "8.3.0", "typescript": "^5.8.3", "undici": "^7.10.0", "update-check": "^1.5.4", "vitest": "~3.2.0", "vitest-websocket-mock": "^0.4.0", "ws": "8.18.0", "xdg-app-paths": "^8.3.0", "xxhash-wasm": "^1.0.1", "yargs": "^17.7.2", "@cloudflare/cli": "1.1.1", "@cloudflare/containers-shared": "0.2.7", "@cloudflare/eslint-config-worker": "1.1.0", "@cloudflare/pages-shared": "^0.13.57", "@cloudflare/workers-shared": "0.18.5", "@cloudflare/workers-tsconfig": "0.0.0"}, "peerDependencies": {"@cloudflare/workers-types": "^4.20250712.0"}, "peerDependenciesMeta": {"@cloudflare/workers-types": {"optional": true}}, "optionalDependencies": {"fsevents": "~2.3.2"}, "engines": {"node": ">=18.0.0"}, "volta": {"extends": "../../package.json"}, "workers-sdk": {"prerelease": true}, "scripts": {"assert-git-version": "node -r esbuild-register scripts/assert-git-version.ts", "build": "pnpm run clean && pnpm tsup && pnpm run generate-json-schema", "check:lint": "eslint . --max-warnings=0", "check:type": "tsc -p ./tsconfig.json && tsc -p ./templates/tsconfig.json", "clean": "rimraf wrangler-dist miniflare-dist emitted-types", "dev": "pnpm run clean && concurrently -c black,blue --kill-others-on-fail false \"pnpm tsup --watch src --watch ../containers-shared/src\" \"pnpm run check:type --watch --preserveWatchOutput\"", "generate-json-schema": "node -r esbuild-register scripts/generate-json-schema.ts", "start": "pnpm run bundle && cross-env NODE_OPTIONS=--enable-source-maps ./bin/wrangler.js", "test": "dotenv -- pnpm run assert-git-version && dotenv -- vitest", "test:ci": "pnpm run test run", "test:debug": "pnpm run test --silent=false --verbose=true", "test:e2e": "dotenv -- vitest run -c ./e2e/vitest.config.mts", "test:watch": "pnpm run test --testTimeout=50000 --watch", "type:tests": "tsc -p ./src/__tests__/tsconfig.json && tsc -p ./e2e/tsconfig.json"}}