{"name": "ts-tqdm", "version": "0.8.6", "description": "A tqdm for node (Typescript).", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["/dist"], "devDependencies": {"@types/node": "^20.3.3", "typescript": "^5.1.6"}, "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["tqdm", "typescript", "ts-tqdm", "progress bar"], "homepage": "https://github.com/delarco/ts-tqdm", "repository": {"type": "git", "url": "https://github.com/delarco/ts-tqdm.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/delarco"}, "license": "MIT"}