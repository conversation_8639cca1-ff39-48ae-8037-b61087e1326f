export declare const Z_NO_FLUSH = 0;
export declare const Z_PARTIAL_FLUSH = 1;
export declare const Z_SYNC_FLUSH = 2;
export declare const Z_FULL_FLUSH = 3;
export declare const Z_FINISH = 4;
export declare const Z_BLOCK = 5;
export declare const Z_OK = 0;
export declare const Z_STREAM_END = 1;
export declare const Z_NEED_DICT = 2;
export declare const Z_ERRNO = -1;
export declare const Z_STREAM_ERROR = -2;
export declare const Z_DATA_ERROR = -3;
export declare const Z_MEM_ERROR = -4;
export declare const Z_BUF_ERROR = -5;
export declare const Z_VERSION_ERROR = -6;
export declare const Z_NO_COMPRESSION = 0;
export declare const Z_BEST_SPEED = 1;
export declare const Z_BEST_COMPRESSION = 9;
export declare const Z_DEFAULT_COMPRESSION = -1;
export declare const Z_FILTERED = 1;
export declare const Z_HUFFMAN_ONLY = 2;
export declare const Z_RLE = 3;
export declare const Z_FIXED = 4;
export declare const Z_DEFAULT_STRATEGY = 0;
export declare const ZLIB_VERNUM = 4865;
export declare const DEFLATE = 1;
export declare const INFLATE = 2;
export declare const GZIP = 3;
export declare const GUNZIP = 4;
export declare const DEFLATERAW = 5;
export declare const INFLATERAW = 6;
export declare const UNZIP = 7;
export declare const BROTLI_DECODE = 8;
export declare const BROTLI_ENCODE = 9;
export declare const Z_MIN_WINDOWBITS = 8;
export declare const Z_MAX_WINDOWBITS = 15;
export declare const Z_DEFAULT_WINDOWBITS = 15;
export declare const Z_MIN_CHUNK = 64;
export declare const Z_MAX_CHUNK: unknown;
export declare const Z_DEFAULT_CHUNK = 16384;
export declare const Z_MIN_MEMLEVEL = 1;
export declare const Z_MAX_MEMLEVEL = 9;
export declare const Z_DEFAULT_MEMLEVEL = 8;
export declare const Z_MIN_LEVEL = -1;
export declare const Z_MAX_LEVEL = 9;
export declare const Z_DEFAULT_LEVEL = -1;
export declare const BROTLI_OPERATION_PROCESS = 0;
export declare const BROTLI_OPERATION_FLUSH = 1;
export declare const BROTLI_OPERATION_FINISH = 2;
export declare const BROTLI_OPERATION_EMIT_METADATA = 3;
export declare const BROTLI_PARAM_MODE = 0;
export declare const BROTLI_MODE_GENERIC = 0;
export declare const BROTLI_MODE_TEXT = 1;
export declare const BROTLI_MODE_FONT = 2;
export declare const BROTLI_DEFAULT_MODE = 0;
export declare const BROTLI_PARAM_QUALITY = 1;
export declare const BROTLI_MIN_QUALITY = 0;
export declare const BROTLI_MAX_QUALITY = 11;
export declare const BROTLI_DEFAULT_QUALITY = 11;
export declare const BROTLI_PARAM_LGWIN = 2;
export declare const BROTLI_MIN_WINDOW_BITS = 10;
export declare const BROTLI_MAX_WINDOW_BITS = 24;
export declare const BROTLI_LARGE_MAX_WINDOW_BITS = 30;
export declare const BROTLI_DEFAULT_WINDOW = 22;
export declare const BROTLI_PARAM_LGBLOCK = 3;
export declare const BROTLI_MIN_INPUT_BLOCK_BITS = 16;
export declare const BROTLI_MAX_INPUT_BLOCK_BITS = 24;
export declare const BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING = 4;
export declare const BROTLI_PARAM_SIZE_HINT = 5;
export declare const BROTLI_PARAM_LARGE_WINDOW = 6;
export declare const BROTLI_PARAM_NPOSTFIX = 7;
export declare const BROTLI_PARAM_NDIRECT = 8;
export declare const BROTLI_DECODER_RESULT_ERROR = 0;
export declare const BROTLI_DECODER_RESULT_SUCCESS = 1;
export declare const BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT = 2;
export declare const BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT = 3;
export declare const BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION = 0;
export declare const BROTLI_DECODER_PARAM_LARGE_WINDOW = 1;
export declare const BROTLI_DECODER_NO_ERROR = 0;
export declare const BROTLI_DECODER_SUCCESS = 1;
export declare const BROTLI_DECODER_NEEDS_MORE_INPUT = 2;
export declare const BROTLI_DECODER_NEEDS_MORE_OUTPUT = 3;
export declare const BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE = -1;
export declare const BROTLI_DECODER_ERROR_FORMAT_RESERVED = -2;
export declare const BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE = -3;
export declare const BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET = -4;
export declare const BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME = -5;
export declare const BROTLI_DECODER_ERROR_FORMAT_CL_SPACE = -6;
export declare const BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE = -7;
export declare const BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT = -8;
export declare const BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1 = -9;
export declare const BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2 = -10;
export declare const BROTLI_DECODER_ERROR_FORMAT_TRANSFORM = -11;
export declare const BROTLI_DECODER_ERROR_FORMAT_DICTIONARY = -12;
export declare const BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS = -13;
export declare const BROTLI_DECODER_ERROR_FORMAT_PADDING_1 = -14;
export declare const BROTLI_DECODER_ERROR_FORMAT_PADDING_2 = -15;
export declare const BROTLI_DECODER_ERROR_FORMAT_DISTANCE = -16;
export declare const BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET = -19;
export declare const BROTLI_DECODER_ERROR_INVALID_ARGUMENTS = -20;
export declare const BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES = -21;
export declare const BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS = -22;
export declare const BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP = -25;
export declare const BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1 = -26;
export declare const BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2 = -27;
export declare const BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES = -30;
export declare const BROTLI_DECODER_ERROR_UNREACHABLE = -31;
