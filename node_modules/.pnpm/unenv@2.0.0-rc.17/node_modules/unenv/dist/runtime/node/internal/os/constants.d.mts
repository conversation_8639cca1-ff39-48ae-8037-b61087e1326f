export declare const UV_UDP_REUSEADDR = 4;
export declare const dlopen: {
	RTLD_LAZY: number
	RTLD_NOW: number
	RTLD_GLOBAL: number
	RTLD_LOCAL: number
	RTLD_DEEPBIND: number
};
export declare const errno: {
	E2BIG: number
	EACCES: number
	EADDRINUSE: number
	EADDRNOTAVAIL: number
	EAFNOSUPPORT: number
	EAGAIN: number
	EALREADY: number
	EBADF: number
	EBADMSG: number
	EBUSY: number
	ECANCELED: number
	ECHILD: number
	ECONNABORTED: number
	ECONNREFUSED: number
	ECONNRESET: number
	EDEADLK: number
	EDESTADDRREQ: number
	EDOM: number
	EDQUOT: number
	EEXIST: number
	EFAULT: number
	EFBIG: number
	EHOSTUNREACH: number
	EIDRM: number
	EILSEQ: number
	EINPROGRESS: number
	EINTR: number
	EINVAL: number
	EIO: number
	EISCONN: number
	EISDIR: number
	ELOOP: number
	EMFILE: number
	EMLINK: number
	EMSGSIZE: number
	EMULTIHOP: number
	ENAMETOOLONG: number
	ENETDOWN: number
	ENETRESET: number
	ENE<PERSON>NREACH: number
	ENFILE: number
	ENOBUFS: number
	ENODATA: number
	ENODEV: number
	ENOENT: number
	ENOEXEC: number
	ENOLCK: number
	ENOLINK: number
	ENOMEM: number
	ENOMSG: number
	ENOPROTOOPT: number
	ENOSPC: number
	ENOSR: number
	ENOSTR: number
	ENOSYS: number
	ENOTCONN: number
	ENOTDIR: number
	ENOTEMPTY: number
	ENOTSOCK: number
	ENOTSUP: number
	ENOTTY: number
	ENXIO: number
	EOPNOTSUPP: number
	EOVERFLOW: number
	EPERM: number
	EPIPE: number
	EPROTO: number
	EPROTONOSUPPORT: number
	EPROTOTYPE: number
	ERANGE: number
	EROFS: number
	ESPIPE: number
	ESRCH: number
	ESTALE: number
	ETIME: number
	ETIMEDOUT: number
	ETXTBSY: number
	EWOULDBLOCK: number
	EXDEV: number
};
export declare const signals: {
	SIGHUP: number
	SIGINT: number
	SIGQUIT: number
	SIGILL: number
	SIGTRAP: number
	SIGABRT: number
	SIGIOT: number
	SIGBUS: number
	SIGFPE: number
	SIGKILL: number
	SIGUSR1: number
	SIGSEGV: number
	SIGUSR2: number
	SIGPIPE: number
	SIGALRM: number
	SIGTERM: number
	SIGCHLD: number
	SIGSTKFLT: number
	SIGCONT: number
	SIGSTOP: number
	SIGTSTP: number
	SIGTTIN: number
	SIGTTOU: number
	SIGURG: number
	SIGXCPU: number
	SIGXFSZ: number
	SIGVTALRM: number
	SIGPROF: number
	SIGWINCH: number
	SIGIO: number
	SIGPOLL: number
	SIGPWR: number
	SIGSYS: number
};
export declare const priority: {
	PRIORITY_LOW: number
	PRIORITY_BELOW_NORMAL: number
	PRIORITY_NORMAL: number
	PRIORITY_ABOVE_NORMAL: number
	PRIORITY_HIGH: number
	PRIORITY_HIGHEST: number
};
