import type nodeHttp2 from "node:http2";
export declare const constants: typeof nodeHttp2.constants;
export declare const createSecureServer: unknown;
export declare const createServer: unknown;
export declare const connect: typeof nodeHttp2.connect;
export declare const performServerHandshake: typeof nodeHttp2.performServerHandshake;
export declare const Http2ServerRequest: typeof nodeHttp2.Http2ServerRequest;
export declare const Http2ServerResponse: typeof nodeHttp2.Http2ServerResponse;
export declare const getDefaultSettings: typeof nodeHttp2.getDefaultSettings;
export declare const getPackedSettings: typeof nodeHttp2.getPackedSettings;
export declare const getUnpackedSettings: typeof nodeHttp2.getUnpackedSettings;
export declare const sensitiveHeaders: typeof nodeHttp2.sensitiveHeaders;
declare const _default: {};
export default _default;
