{"name": "unenv", "version": "2.0.0-rc.17", "description": "", "repository": "unjs/unenv", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./package.json": "./package.json", "./mock/proxy-cjs": {"types": "./lib/mock.d.cts", "default": "./lib/mock.cjs"}, "./mock/proxy-cjs/*": {"types": "./lib/mock.d.cts", "default": "./lib/mock.cjs"}, "./*": {"types": "./dist/runtime/*.d.mts", "default": "./dist/runtime/*.mjs"}}, "types": "./dist/index.d.mts", "files": ["dist", "lib"], "scripts": {"build": "obuild", "build:watch": "pnpm node-ts --watch ./scripts/build.ts", "node-ts": "node --disable-warning=ExperimentalWarning --experimental-strip-types", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "pnpm node-ts ./test/node-coverage.ts && automd && eslint --fix . && prettier -w src test", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release --push --publish --prerelease --publishTag rc", "test": "pnpm lint && pnpm test:types && pnpm build && pnpm vitest --run", "test:types": "tsc --noEmit"}, "dependencies": {"defu": "^6.1.4", "exsolve": "^1.0.4", "ohash": "^2.0.11", "pathe": "^2.0.3", "ufo": "^1.6.1"}, "devDependencies": {"@parcel/watcher": "^2.5.1", "@types/node": "^22.14.1", "@vitest/coverage-v8": "^3.1.1", "automd": "^0.4.0", "changelogen": "^0.6.1", "consola": "^3.4.2", "esbuild": "^0.25.2", "eslint": "^9.24.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "magic-string": "^0.30.17", "obuild": "^0.0.6", "prettier": "^3.5.3", "tinyexec": "^1.0.1", "typescript": "^5.8.3", "vitest": "^3.1.1", "workerd": "^1.20250416.0", "wrangler": "^4.11.1"}, "packageManager": "pnpm@10.6.3"}