{"name": "@unrs/resolver-binding-darwin-x64", "version": "1.11.1", "cpu": ["x64"], "main": "resolver.darwin-x64.node", "files": ["resolver.darwin-x64.node"], "description": "UnRS Resolver Node API with PNP support", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "homepage": "https://github.com/unrs/unrs-resolver#readme", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "repository": "git+https://github.com/unrs/unrs-resolver.git", "os": ["darwin"]}