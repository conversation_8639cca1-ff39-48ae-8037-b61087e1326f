{"name": "yargs", "version": "18.0.0", "description": "yargs the modern, pirate-themed, successor to optimist.", "main": "./index.mjs", "exports": {"./package.json": "./package.json", "./helpers": "./helpers/helpers.mjs", "./browser": {"types": "./browser.d.ts", "import": "./browser.mjs"}, ".": "./index.mjs", "./yargs": "./index.mjs"}, "type": "module", "module": "./index.mjs", "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "files": ["browser.mjs", "browser.d.ts", "helpers/*.js", "helpers/*", "index.mjs", "build", "locales", "LICENSE", "lib/platform-shims/*.mjs", "!*.d.ts", "!**/*.d.ts"], "dependencies": {"cliui": "^9.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "string-width": "^7.2.0", "y18n": "^5.0.5", "yargs-parser": "^22.0.0"}, "devDependencies": {"@babel/eslint-parser": "^7.26.10", "@babel/preset-typescript": "^7.26.0", "@types/chai": "^4.2.11", "@types/mocha": "^9.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "browserslist-generator": "^2.0.1", "c8": "^9.1.0", "chai": "^4.2.0", "chalk": "^4.0.0", "cpr": "^3.0.1", "cross-env": "^7.0.2", "cross-spawn": "^7.0.6", "eslint": "^8.57.1", "eslint-plugin-prettier": "^5.1.2", "gts": "^5.2.0", "hashish": "0.0.4", "mocha": "^10.8.2", "rimraf": "^3.0.2", "typescript": "^5.8.3", "which": "^2.0.0", "yargs-test-extends": "^1.0.1"}, "scripts": {"fix": "gts fix && npm run fix:js", "fix:js": "eslint . --ext mjs --ext js --fix", "posttest": "npm run check", "test": "c8 mocha --enable-source-maps ./test/*.mjs --require ./test/before.mjs --timeout=24000 --check-leaks", "test:esm": "c8 mocha --enable-source-maps ./test/esm/*.mjs --check-leaks", "coverage": "c8 report --check-coverage", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json", "compile": "rimraf build && tsc", "check": "gts lint && npm run check:js", "check:js": "eslint . --ext mjs --ext js", "clean": "gts clean", "watch": "rimraf build && npm:watch:tsc", "watch:tsc": "tsc --watch"}, "repository": {"type": "git", "url": "https://github.com/yargs/yargs.git"}, "homepage": "https://yargs.js.org/", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=23"}}