import Dispatcher from ".pnpm/undici-types@6.21.0/node_modules/undici-types/dispatcher";

export declare class Redirect<PERSON><PERSON>ler implements Dispatcher.DispatchHandlers {
  constructor(
    dispatch: Dispatcher,
    maxRedirections: number,
    opts: Dispatcher.DispatchOptions,
    handler: Dispatcher.DispatchHandlers,
    redirectionLimitReached: boolean
  );
}

export declare class DecoratorHandler implements Dispatcher.DispatchHandlers {
  constructor(handler: Dispatcher.DispatchHandlers);
}
