import { BaseComponent } from '../../component.js';
import type { ErrorInfoProps } from '../../types.js';
/**
 * Displays the error info including the response status text,
 * error name, error message and the hint.
 */
export declare class ErrorInfo extends BaseComponent<ErrorInfoProps> {
    cssFile: URL;
    /**
     * The toHTML method is used to output the HTML for the
     * web view
     */
    toHTML(props: ErrorInfoProps): Promise<string>;
    /**
     * The toANSI method is used to output the text for the console
     */
    toANSI(props: ErrorInfoProps): Promise<string>;
}
