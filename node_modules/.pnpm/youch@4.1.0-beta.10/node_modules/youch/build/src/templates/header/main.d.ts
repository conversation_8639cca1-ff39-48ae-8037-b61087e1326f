import { BaseComponent } from '../../component.js';
import type { ComponentSharedProps } from '../../types.js';
/**
 * Renders the header for the error page. It contains only the
 * theme-switcher for now
 */
export declare class Header extends BaseComponent<ComponentSharedProps> {
    cssFile: URL;
    scriptFile: URL;
    /**
     * The toHTML method is used to output the HTML for the
     * web view
     */
    toHTML(): Promise<string>;
    /**
     * The toANSI method is used to output the text for the console
     */
    toANSI(): Promise<string>;
}
