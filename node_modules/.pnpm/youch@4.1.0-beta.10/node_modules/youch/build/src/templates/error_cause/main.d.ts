import { BaseComponent } from '../../component.js';
import type { ErrorCauseProps } from '../../types.js';
/**
 * Displays the Error cause as a formatted value
 */
export declare class ErrorCause extends BaseComponent<ErrorCauseProps> {
    cssFile: URL;
    /**
     * The toHTML method is used to output the HTML for the
     * web view
     */
    toHTML(props: ErrorCause['$props']): Promise<string>;
    /**
     * The toANSI method is used to output the text for the console
     */
    toANSI(props: ErrorCauseProps): Promise<string>;
}
